// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  avatar    String?
  role      UserRole @default(USER)
  
  // Company association
  companyId String?
  company   Company? @relation(fields: [companyId], references: [id])
  
  // Authentication
  password     String?
  emailVerified DateTime?
  
  // OAuth accounts
  accounts Account[]
  sessions Session[]
  
  // User preferences
  preferences Json @default("{}")
  
  // Usage tracking
  planType        PlanType @default(FREE)
  subscriptionId  String?
  usageQuota      Json     @default("{}")
  
  // Relationships
  documents       Document[]
  translations    Translation[]
  glossaries      Glossary[]
  liveSessions    LiveSession[]
  learningCards   LearningCard[]
  analytics       UserAnalytics[]
  notifications   Notification[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("users")
}

model Company {
  id          String @id @default(cuid())
  name        String
  domain      String? @unique
  logo        String?
  settings    Json   @default("{}")
  
  // Subscription
  planType       PlanType @default(TEAM)
  subscriptionId String?
  
  // Relationships
  users       User[]
  glossaries  Glossary[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("companies")
}

// Authentication models (NextAuth.js compatible)
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Document management
model Document {
  id               String           @id @default(cuid())
  userId           String
  user             User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // File information
  originalFilename String
  filename         String
  mimeType         String
  fileSize         Int
  s3Key            String
  s3Bucket         String
  
  // Processing status
  status           DocumentStatus   @default(PROCESSING)
  
  // OCR and text extraction
  ocrText          String?
  ocrConfidence    Float?
  languageDetected String?
  
  // Translation
  translatedText   String?
  translationId    String?
  translation      Translation?     @relation(fields: [translationId], references: [id])
  
  // Metadata
  metadata         Json             @default("{}")
  tags             String[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("documents")
}

// Translation services
model Translation {
  id               String            @id @default(cuid())
  userId           String
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Translation content
  sourceText       String
  translatedText   String
  sourceLanguage   String
  targetLanguage   String
  
  // Quality metrics
  confidenceScore  Float?
  provider         TranslationProvider
  model            String?
  
  // Context and glossary
  context          String?
  glossaryId       String?
  glossary         Glossary?         @relation(fields: [glossaryId], references: [id])
  
  // Usage tracking
  characterCount   Int
  processingTime   Int? // milliseconds
  
  // Relationships
  documents        Document[]
  
  // Metadata
  metadata         Json              @default("{}")
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("translations")
}

// Glossary management
model Glossary {
  id          String @id @default(cuid())
  userId      String?
  user        User?  @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyId   String?
  company     Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  name        String
  description String?
  isShared    Boolean @default(false)
  isActive    Boolean @default(true)
  
  // Terms stored as JSON array
  terms       Json    @default("[]")
  
  // Usage statistics
  usageCount  Int     @default(0)
  
  // Relationships
  translations Translation[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("glossaries")
}

// Live translation sessions
model LiveSession {
  id              String            @id @default(cuid())
  userId          String
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  name            String?
  status          LiveSessionStatus @default(ACTIVE)
  
  // Session configuration
  sourceLanguage  String            @default("ar")
  targetLanguage  String            @default("en")
  
  // Duration tracking
  startedAt       DateTime          @default(now())
  endedAt         DateTime?
  durationSeconds Int               @default(0)
  
  // Content
  transcript      String?
  translation     String?
  
  // Metadata
  metadata        Json              @default("{}")
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("live_sessions")
}

// Learning system
model LearningCard {
  id              String   @id @default(cuid())
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  sourceText      String
  targetText      String
  context         String?
  
  // Spaced repetition
  difficultyLevel Int      @default(1)
  nextReview      DateTime
  reviewCount     Int      @default(0)
  successRate     Float    @default(0.0)
  
  // Metadata
  metadata        Json     @default("{}")
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("learning_cards")
}

// Analytics and reporting
model UserAnalytics {
  id              String   @id @default(cuid())
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  date            DateTime @default(now())
  
  // Usage metrics
  documentsProcessed    Int @default(0)
  charactersTranslated  Int @default(0)
  liveMinutesUsed      Int @default(0)
  glossaryTermsUsed    Int @default(0)
  
  // Quality metrics
  averageConfidence    Float?
  userSatisfaction     Float?
  
  // Metadata
  metadata        Json     @default("{}")
  
  @@unique([userId, date])
  @@map("user_analytics")
}

// Notifications
model Notification {
  id        String             @id @default(cuid())
  userId    String
  user      User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  type      NotificationType
  title     String
  message   String
  data      Json?              @default("{}")
  
  isRead    Boolean            @default(false)
  readAt    DateTime?
  
  createdAt DateTime @default(now())
  
  @@map("notifications")
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum PlanType {
  FREE
  PROFESSIONAL
  TEAM
  ENTERPRISE
}

enum DocumentStatus {
  PROCESSING
  COMPLETED
  FAILED
  DELETED
}

enum TranslationProvider {
  OPENAI
  DEEPL
  GOOGLE
  AZURE
}

enum LiveSessionStatus {
  ACTIVE
  PAUSED
  COMPLETED
  FAILED
}

enum NotificationType {
  SYSTEM
  TRANSLATION_COMPLETE
  QUOTA_WARNING
  QUOTA_EXCEEDED
  SUBSCRIPTION_EXPIRING
  FEATURE_ANNOUNCEMENT
}
