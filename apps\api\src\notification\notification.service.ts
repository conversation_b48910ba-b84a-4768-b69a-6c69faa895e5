import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { NotificationType } from '@prisma/client';

export interface CreateNotificationDto {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  inApp: boolean;
  types: {
    [key in NotificationType]: boolean;
  };
}

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(private readonly prisma: PrismaService) {}

  async createNotification(createDto: CreateNotificationDto) {
    try {
      const notification = await this.prisma.notification.create({
        data: {
          userId: createDto.userId,
          type: createDto.type,
          title: createDto.title,
          message: createDto.message,
          data: createDto.data || {},
        },
      });

      this.logger.log(`Notification created: ${notification.id} for user ${createDto.userId}`);
      
      // In a real implementation, you would also send push notifications, emails, etc.
      await this.sendNotification(notification);

      return notification;
    } catch (error) {
      this.logger.error('Error creating notification:', error);
      throw error;
    }
  }

  async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 20,
    unreadOnly: boolean = false,
  ) {
    try {
      const skip = (page - 1) * limit;
      const where: any = { userId };

      if (unreadOnly) {
        where.isRead = false;
      }

      const [notifications, total, unreadCount] = await Promise.all([
        this.prisma.notification.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.notification.count({ where }),
        this.prisma.notification.count({
          where: { userId, isRead: false },
        }),
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        unreadCount,
      };
    } catch (error) {
      this.logger.error(`Error getting notifications for user ${userId}:`, error);
      throw error;
    }
  }

  async markAsRead(userId: string, notificationId: string) {
    try {
      const notification = await this.prisma.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!notification) {
        throw new Error('Notification not found');
      }

      if (!notification.isRead) {
        await this.prisma.notification.update({
          where: { id: notificationId },
          data: {
            isRead: true,
            readAt: new Date(),
          },
        });

        this.logger.log(`Notification marked as read: ${notificationId} by user ${userId}`);
      }

      return notification;
    } catch (error) {
      this.logger.error(`Error marking notification as read ${notificationId} for user ${userId}:`, error);
      throw error;
    }
  }

  async markAllAsRead(userId: string) {
    try {
      const result = await this.prisma.notification.updateMany({
        where: {
          userId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      this.logger.log(`Marked ${result.count} notifications as read for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Error marking all notifications as read for user ${userId}:`, error);
      throw error;
    }
  }

  async deleteNotification(userId: string, notificationId: string) {
    try {
      const notification = await this.prisma.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!notification) {
        throw new Error('Notification not found');
      }

      await this.prisma.notification.delete({
        where: { id: notificationId },
      });

      this.logger.log(`Notification deleted: ${notificationId} by user ${userId}`);
    } catch (error) {
      this.logger.error(`Error deleting notification ${notificationId} for user ${userId}:`, error);
      throw error;
    }
  }

  // Convenience methods for common notification types
  async notifyTranslationComplete(userId: string, documentId: string, translationId: string) {
    return await this.createNotification({
      userId,
      type: NotificationType.TRANSLATION_COMPLETE,
      title: 'Translation Complete',
      message: 'Your document translation has been completed successfully.',
      data: {
        documentId,
        translationId,
      },
    });
  }

  async notifyQuotaWarning(userId: string, quotaType: string, usagePercentage: number) {
    return await this.createNotification({
      userId,
      type: NotificationType.QUOTA_WARNING,
      title: 'Quota Warning',
      message: `You have used ${usagePercentage}% of your ${quotaType} quota this month.`,
      data: {
        quotaType,
        usagePercentage,
      },
    });
  }

  async notifyQuotaExceeded(userId: string, quotaType: string) {
    return await this.createNotification({
      userId,
      type: NotificationType.QUOTA_EXCEEDED,
      title: 'Quota Exceeded',
      message: `You have exceeded your ${quotaType} quota. Please upgrade your plan to continue.`,
      data: {
        quotaType,
      },
    });
  }

  async notifySubscriptionExpiring(userId: string, daysRemaining: number) {
    return await this.createNotification({
      userId,
      type: NotificationType.SUBSCRIPTION_EXPIRING,
      title: 'Subscription Expiring',
      message: `Your subscription will expire in ${daysRemaining} days. Please renew to continue using all features.`,
      data: {
        daysRemaining,
      },
    });
  }

  async notifyFeatureAnnouncement(userId: string, featureName: string, description: string) {
    return await this.createNotification({
      userId,
      type: NotificationType.FEATURE_ANNOUNCEMENT,
      title: `New Feature: ${featureName}`,
      message: description,
      data: {
        featureName,
      },
    });
  }

  async getNotificationPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { preferences: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      const preferences = user.preferences as any;
      
      return {
        email: preferences?.notifications?.email ?? true,
        push: preferences?.notifications?.push ?? true,
        inApp: preferences?.notifications?.inApp ?? true,
        types: {
          SYSTEM: preferences?.notifications?.types?.SYSTEM ?? true,
          TRANSLATION_COMPLETE: preferences?.notifications?.types?.TRANSLATION_COMPLETE ?? true,
          QUOTA_WARNING: preferences?.notifications?.types?.QUOTA_WARNING ?? true,
          QUOTA_EXCEEDED: preferences?.notifications?.types?.QUOTA_EXCEEDED ?? true,
          SUBSCRIPTION_EXPIRING: preferences?.notifications?.types?.SUBSCRIPTION_EXPIRING ?? true,
          FEATURE_ANNOUNCEMENT: preferences?.notifications?.types?.FEATURE_ANNOUNCEMENT ?? true,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting notification preferences for user ${userId}:`, error);
      throw error;
    }
  }

  async updateNotificationPreferences(
    userId: string,
    preferences: Partial<NotificationPreferences>,
  ) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { preferences: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      const currentPreferences = user.preferences as any;
      const updatedPreferences = {
        ...currentPreferences,
        notifications: {
          ...currentPreferences?.notifications,
          ...preferences,
        },
      };

      await this.prisma.user.update({
        where: { id: userId },
        data: {
          preferences: updatedPreferences,
        },
      });

      this.logger.log(`Notification preferences updated for user ${userId}`);
      return updatedPreferences.notifications;
    } catch (error) {
      this.logger.error(`Error updating notification preferences for user ${userId}:`, error);
      throw error;
    }
  }

  private async sendNotification(notification: any) {
    try {
      // Get user preferences
      const preferences = await this.getNotificationPreferences(notification.userId);

      // Check if user wants this type of notification
      if (!preferences.types[notification.type]) {
        this.logger.log(`Notification type ${notification.type} disabled for user ${notification.userId}`);
        return;
      }

      // Send email notification
      if (preferences.email) {
        await this.sendEmailNotification(notification);
      }

      // Send push notification
      if (preferences.push) {
        await this.sendPushNotification(notification);
      }

      // In-app notifications are handled by the database record itself
    } catch (error) {
      this.logger.error('Error sending notification:', error);
    }
  }

  private async sendEmailNotification(notification: any) {
    // Mock email sending - replace with actual email service
    this.logger.log(`Mock email sent for notification ${notification.id}`);
  }

  private async sendPushNotification(notification: any) {
    // Mock push notification - replace with actual push service
    this.logger.log(`Mock push notification sent for notification ${notification.id}`);
  }
}
