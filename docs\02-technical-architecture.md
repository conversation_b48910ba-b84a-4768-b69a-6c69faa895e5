# Tarjama.ai - Technical Architecture Design

## 1. System Architecture Overview

### High-Level Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        CLIENT LAYER                             │
├─────────────────────────────────────────────────────────────────┤
│  Web App (Next.js 14)  │  Mobile App (React Native)  │ Browser │
│  • React 19            │  • Expo 50                   │ Plugin  │
│  • TypeScript          │  • Offline OCR               │         │
│  • Tailwind + shadcn   │  • Background sync           │         │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      EDGE & CDN LAYER                           │
├─────────────────────────────────────────────────────────────────┤
│  CloudFront CDN  │  AWS WAF  │  Route 53  │  Load Balancer     │
│  • Global cache  │  • DDoS   │  • DNS     │  • SSL termination │
│  • Asset delivery│  • Rate   │  • Health  │  • Auto-scaling    │
│                  │    limit  │    checks  │                    │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     API GATEWAY LAYER                           │
├─────────────────────────────────────────────────────────────────┤
│  Kong Gateway  │  Auth Service  │  Rate Limiting  │  Analytics  │
│  • REST APIs   │  • JWT/OAuth   │  • Per-user     │  • Metrics  │
│  • WebSocket   │  • Auth0 SSO   │  • Per-endpoint │  • Logging  │
│  • GraphQL     │  • RBAC        │  • Quotas       │  • Tracing  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                   MICROSERVICES LAYER                           │
├─────────────────────────────────────────────────────────────────┤
│ File Service │ OCR Service │ STT Service │ Translation Service  │
│ • S3 upload  │ • Vision AI │ • Whisper   │ • GPT-4o orchestr.  │
│ • Presigned  │ • Tesseract │ • Real-time │ • DeepL fallback    │
│ • Validation │ • Arabic    │ • Batch     │ • Azure backup      │
│              │   models    │ • WebRTC    │ • Confidence score  │
├──────────────┼─────────────┼─────────────┼─────────────────────┤
│ PDF Service  │ Glossary    │ Learning    │ Notification Svc    │
│ • Composer   │ Service     │ Service     │ • Email/SMS         │
│ • Renderer   │ • Personal  │ • Flashcard │ • Push notifications│
│ • Watermark  │ • Team      │ • Spaced    │ • Webhooks          │
│ • Export     │ • Auto-repl │   repetition│ • Slack integration │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     DATA LAYER                                  │
├─────────────────────────────────────────────────────────────────┤
│ PostgreSQL 16 │  Redis Cluster  │  Vector DB    │  Object Store │
│ • User data   │  • Sessions     │  • Pinecone   │  • AWS S3     │
│ • Metadata    │  • Cache        │  • Embeddings │  • MinIO      │
│ • Analytics   │  • Pub/Sub      │  • Similarity │  • Backups    │
│ • Audit logs  │  • Rate limits  │    search     │  • Archives   │
└─────────────────────────────────────────────────────────────────┘
```

## 2. Technology Stack Rationale

### Frontend Stack
**Next.js 14 + React 19 + TypeScript**
- **Why**: Server-side rendering for SEO, excellent developer experience, strong TypeScript support
- **Benefits**: Fast initial load, automatic code splitting, built-in optimization
- **Alternatives Considered**: Vue.js (smaller ecosystem), Angular (too heavy)

**Tailwind CSS + shadcn/ui**
- **Why**: Rapid prototyping, consistent design system, accessibility built-in
- **Benefits**: Smaller bundle size, customizable components, dark mode support
- **Alternatives Considered**: Material-UI (too opinionated), Chakra UI (performance concerns)

**Zustand for State Management**
- **Why**: Lightweight, TypeScript-first, minimal boilerplate
- **Benefits**: Better performance than Redux, simpler than Context API
- **Alternatives Considered**: Redux Toolkit (overkill), Jotai (too new)

### Backend Stack
**NestJS + TypeScript**
- **Why**: Enterprise-grade architecture, excellent TypeScript support, modular design
- **Benefits**: Built-in dependency injection, decorators, microservices support
- **Alternatives Considered**: Express.js (too minimal), Fastify (smaller ecosystem)

**PostgreSQL 16 + Prisma ORM**
- **Why**: ACID compliance, JSON support, excellent performance, type-safe queries
- **Benefits**: Advanced indexing, full-text search, vector extensions (pgvector)
- **Alternatives Considered**: MongoDB (consistency issues), MySQL (limited JSON support)

**Redis Cluster**
- **Why**: High-performance caching, pub/sub, session storage
- **Benefits**: Sub-millisecond latency, horizontal scaling, persistence options
- **Alternatives Considered**: Memcached (limited features), DragonflyDB (too new)

### AI/ML Stack
**OpenAI GPT-4o + Whisper**
- **Why**: Best-in-class Arabic support, consistent API, rapid improvements
- **Benefits**: Context understanding, fine-tuning capabilities, multimodal support
- **Alternatives Considered**: Claude (limited Arabic), Gemini (inconsistent quality)

**Google Cloud Vision API**
- **Why**: Superior Arabic OCR accuracy, handwriting recognition
- **Benefits**: 99%+ accuracy on printed Arabic, supports multiple formats
- **Alternatives Considered**: AWS Textract (weaker Arabic), Azure (higher latency)

**DeepL API (Fallback)**
- **Why**: High-quality translations, competitive pricing
- **Benefits**: Excellent for formal documents, consistent terminology
- **Alternatives Considered**: Azure Translator (good but expensive), Google Translate (lower quality)

### Infrastructure Stack
**AWS EKS (Kubernetes)**
- **Why**: Container orchestration, auto-scaling, service mesh capabilities
- **Benefits**: High availability, resource efficiency, vendor independence
- **Alternatives Considered**: ECS (less flexible), GKE (vendor lock-in)

**Terraform + GitOps**
- **Why**: Infrastructure as code, version control, reproducible deployments
- **Benefits**: Multi-environment consistency, disaster recovery, compliance
- **Alternatives Considered**: CloudFormation (AWS-only), Pulumi (smaller community)

## 3. Detailed Service Architecture

### File Processing Pipeline
```
Upload Request → S3 Presigned URL → Client Upload → S3 Event → Lambda Trigger
                                                                      ↓
OCR Service ← SQS Queue ← File Validation ← Virus Scan ← Metadata Extract
     ↓
Text Extraction → Language Detection → Translation Service → PDF Composer
     ↓                                         ↓                    ↓
Vector Store    →    Glossary Service    →    Export Service   →   Notification
```

### Real-Time Translation Pipeline
```
WebRTC Stream → Whisper STT → Language Detection → Translation Orchestrator
                                                            ↓
                                                   Confidence Scoring
                                                            ↓
                                              WebSocket → Client Display
                                                            ↓
                                                   Learning Service Update
```

### Translation Orchestrator Logic
```typescript
interface TranslationRequest {
  text: string;
  sourceLanguage: 'ar' | 'en';
  targetLanguage: 'ar' | 'en';
  context: 'construction' | 'legal' | 'technical';
  glossary?: string[];
}

class TranslationOrchestrator {
  async translate(request: TranslationRequest): Promise<TranslationResult> {
    // 1. Apply personal glossary
    const preprocessed = this.applyGlossary(request.text, request.glossary);
    
    // 2. Parallel translation requests
    const [gptResult, deeplResult, azureResult] = await Promise.allSettled([
      this.gptTranslate(preprocessed, request.context),
      this.deeplTranslate(preprocessed),
      this.azureTranslate(preprocessed)
    ]);
    
    // 3. Confidence scoring and selection
    const bestTranslation = this.selectBestTranslation([
      gptResult, deeplResult, azureResult
    ]);
    
    // 4. Post-processing
    return this.postProcess(bestTranslation, request.glossary);
  }
}
```

## 4. Data Architecture

### Database Schema (Core Tables)
```sql
-- Users and Authentication
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  company_id UUID REFERENCES companies(id),
  role user_role NOT NULL DEFAULT 'user',
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Document Management
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  original_filename VARCHAR(255) NOT NULL,
  s3_key VARCHAR(500) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  file_size BIGINT NOT NULL,
  language_detected VARCHAR(10),
  ocr_text TEXT,
  translation_text TEXT,
  status document_status DEFAULT 'processing',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Glossary Management
CREATE TABLE glossaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  company_id UUID REFERENCES companies(id),
  name VARCHAR(255) NOT NULL,
  is_shared BOOLEAN DEFAULT FALSE,
  terms JSONB NOT NULL, -- [{"source": "مقاول", "target": "contractor", "context": "construction"}]
  created_at TIMESTAMP DEFAULT NOW()
);

-- Learning System
CREATE TABLE learning_cards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  source_text VARCHAR(500) NOT NULL,
  target_text VARCHAR(500) NOT NULL,
  context VARCHAR(100),
  difficulty_level INTEGER DEFAULT 1,
  next_review TIMESTAMP,
  review_count INTEGER DEFAULT 0,
  success_rate DECIMAL(3,2) DEFAULT 0.0,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Vector Database Schema (Pinecone)
```python
# Document embeddings for similarity search
index_config = {
    "name": "tarjama-documents",
    "dimension": 1536,  # OpenAI ada-002 embeddings
    "metric": "cosine",
    "metadata_config": {
        "indexed": ["user_id", "document_type", "language", "company_id"]
    }
}

# Glossary term embeddings for context matching
glossary_index = {
    "name": "tarjama-glossary",
    "dimension": 1536,
    "metric": "cosine",
    "metadata_config": {
        "indexed": ["user_id", "domain", "confidence_score"]
    }
}
```

## 5. Security Architecture

### Authentication & Authorization
- **Auth0 Integration**: SSO, MFA, social logins
- **JWT Tokens**: Short-lived access tokens (15 min), refresh tokens (7 days)
- **RBAC**: Role-based access control (Admin, Manager, User, Viewer)
- **API Keys**: For enterprise integrations, rate-limited and scoped

### Data Protection
- **Encryption at Rest**: AES-256 for databases, S3 server-side encryption
- **Encryption in Transit**: TLS 1.3 for all communications
- **Key Management**: AWS KMS with automatic rotation
- **Data Anonymization**: PII scrubbing for analytics and ML training

### Compliance Framework
- **GDPR**: Right to deletion, data portability, consent management
- **PDPL**: Data localization, breach notification, privacy by design
- **SOC 2 Type II**: Annual audits, continuous monitoring
- **ISO 27001**: Information security management system

## 6. Performance & Scalability

### Performance Targets
- **API Response Time**: < 200ms (95th percentile)
- **File Upload**: < 30 seconds for 10MB PDF
- **OCR Processing**: < 60 seconds for 20-page document
- **Real-time Translation**: < 2 seconds end-to-end latency
- **Availability**: 99.9% uptime SLA

### Scaling Strategy
- **Horizontal Scaling**: Kubernetes HPA based on CPU/memory/custom metrics
- **Database Scaling**: Read replicas, connection pooling, query optimization
- **Caching Strategy**: Multi-layer caching (CDN, Redis, application-level)
- **Auto-scaling**: Predictive scaling based on usage patterns

### Monitoring & Observability
- **Metrics**: Prometheus + Grafana dashboards
- **Logging**: Structured logging with ELK stack
- **Tracing**: OpenTelemetry for distributed tracing
- **Alerting**: PagerDuty integration for critical issues
- **Error Tracking**: Sentry for application errors

## Next Steps
1. Create detailed API specifications (OpenAPI/Swagger)
2. Design database migration strategy
3. Set up development and staging environments
4. Implement CI/CD pipeline with automated testing
5. Create infrastructure as code (Terraform modules)
