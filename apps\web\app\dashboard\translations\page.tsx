"use client";

import { useState } from "react";
import Link from "next/link";

interface Translation {
  id: string;
  sourceText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  createdAt: string;
  type: "text" | "document" | "live";
  status: "completed" | "processing" | "failed";
}

export default function TranslationsPage() {
  const [translations] = useState<Translation[]>([
    {
      id: "1",
      sourceText: "مواد البناء عالية الجودة",
      translatedText: "High-quality construction materials",
      sourceLanguage: "ar",
      targetLanguage: "en",
      createdAt: "2024-01-15T10:30:00Z",
      type: "text",
      status: "completed",
    },
    {
      id: "2",
      sourceText: "Foundation reinforcement specifications",
      translatedText: "مواصفات تعزيز الأساس",
      sourceLanguage: "en",
      targetLanguage: "ar",
      createdAt: "2024-01-15T09:15:00Z",
      type: "document",
      status: "completed",
    },
    {
      id: "3",
      sourceText: "Safety protocols for construction site",
      translatedText: "بروتوكولات السلامة لموقع البناء",
      sourceLanguage: "en",
      targetLanguage: "ar",
      createdAt: "2024-01-15T08:45:00Z",
      type: "live",
      status: "completed",
    },
  ]);

  const [filter, setFilter] = useState<"all" | "text" | "document" | "live">("all");
  const [searchQuery, setSearchQuery] = useState("");

  const filteredTranslations = translations.filter((translation) => {
    const matchesFilter = filter === "all" || translation.type === filter;
    const matchesSearch = 
      translation.sourceText.toLowerCase().includes(searchQuery.toLowerCase()) ||
      translation.translatedText.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "text": return "💬";
      case "document": return "📄";
      case "live": return "🎤";
      default: return "📝";
    }
  };

  const getLanguageFlag = (lang: string) => {
    return lang === "ar" ? "🇸🇦" : "🇺🇸";
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white">Translation History</h1>
              <p className="text-gray-400 mt-2">View and manage all your translations</p>
            </div>
            <Link
              href="/demo"
              className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              New Translation
            </Link>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search translations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/20"
              />
            </div>
            <div className="flex gap-2">
              {["all", "text", "document", "live"].map((type) => (
                <button
                  key={type}
                  onClick={() => setFilter(type as any)}
                  className={`px-4 py-3 rounded-lg font-medium transition-colors capitalize ${
                    filter === type
                      ? "bg-white text-black"
                      : "bg-gray-800 text-gray-300 hover:bg-gray-700"
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Translations List */}
        <div className="space-y-4">
          {filteredTranslations.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold text-white mb-2">No translations found</h3>
              <p className="text-gray-400 mb-6">
                {searchQuery ? "Try adjusting your search terms" : "Start translating to see your history here"}
              </p>
              <Link
                href="/demo"
                className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors inline-block"
              >
                Create First Translation
              </Link>
            </div>
          ) : (
            filteredTranslations.map((translation) => (
              <div
                key={translation.id}
                className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 hover:bg-gray-900/70 transition-all duration-200"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getTypeIcon(translation.type)}</span>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-gray-300 capitalize">
                          {translation.type} Translation
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          translation.status === "completed" 
                            ? "bg-green-900/50 text-green-300"
                            : translation.status === "processing"
                            ? "bg-yellow-900/50 text-yellow-300"
                            : "bg-red-900/50 text-red-300"
                        }`}>
                          {translation.status}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">{formatDate(translation.createdAt)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">{getLanguageFlag(translation.sourceLanguage)}</span>
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                    <span className="text-sm">{getLanguageFlag(translation.targetLanguage)}</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-2">Source Text</h4>
                    <p className="text-white bg-gray-800/50 rounded-lg p-4 text-sm leading-relaxed">
                      {translation.sourceText}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-2">Translation</h4>
                    <p className="text-white bg-gray-800/50 rounded-lg p-4 text-sm leading-relaxed">
                      {translation.translatedText}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-700">
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <span>Characters: {translation.sourceText.length}</span>
                    <span>•</span>
                    <span>Words: {translation.sourceText.split(" ").length}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="text-gray-400 hover:text-white transition-colors p-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </button>
                    <button className="text-gray-400 hover:text-white transition-colors p-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </button>
                    <button className="text-gray-400 hover:text-white transition-colors p-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Pagination */}
        {filteredTranslations.length > 0 && (
          <div className="flex items-center justify-between mt-8">
            <p className="text-sm text-gray-400">
              Showing {filteredTranslations.length} of {translations.length} translations
            </p>
            <div className="flex items-center gap-2">
              <button className="px-3 py-2 text-sm bg-gray-800 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
                Previous
              </button>
              <button className="px-3 py-2 text-sm bg-white text-black rounded-lg font-medium">
                1
              </button>
              <button className="px-3 py-2 text-sm bg-gray-800 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
