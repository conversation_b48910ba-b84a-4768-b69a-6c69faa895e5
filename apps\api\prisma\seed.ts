import { PrismaClient, UserRole, PlanType } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: adminPassword,
      role: UserRole.ADMIN,
      planType: PlanType.ENTERPRISE,
      emailVerified: new Date(),
      preferences: {
        language: 'en',
        theme: 'light',
        notifications: {
          email: true,
          push: true,
        },
      },
      usageQuota: {
        documents: 0,
        liveMinutes: 0,
        charactersTranslated: 0,
      },
    },
  });

  console.log('✅ Created admin user:', admin.email);

  // Create demo company
  const demoCompany = await prisma.company.upsert({
    where: { domain: 'demo.tarjama.ai' },
    update: {},
    create: {
      name: 'Demo Construction Company',
      domain: 'demo.tarjama.ai',
      planType: PlanType.TEAM,
      settings: {
        allowSharedGlossaries: true,
        requireApproval: false,
        maxUsers: 50,
        features: {
          liveTranslation: true,
          glossarySharing: true,
          analytics: true,
          apiAccess: true,
        },
      },
    },
  });

  console.log('✅ Created demo company:', demoCompany.name);

  // Create demo users
  const demoUsers = [
    {
      email: '<EMAIL>',
      name: 'Ahmed Al-Rashid',
      role: UserRole.USER,
      planType: PlanType.PROFESSIONAL,
    },
    {
      email: '<EMAIL>',
      name: 'Sarah Mitchell',
      role: UserRole.USER,
      planType: PlanType.PROFESSIONAL,
    },
    {
      email: '<EMAIL>',
      name: 'Fatima Al-Zahra',
      role: UserRole.USER,
      planType: PlanType.TEAM,
    },
  ];

  for (const userData of demoUsers) {
    const userPassword = await bcrypt.hash('demo123', 12);
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        password: userPassword,
        companyId: demoCompany.id,
        emailVerified: new Date(),
        preferences: {
          language: 'en',
          theme: 'light',
          notifications: {
            email: true,
            push: true,
          },
        },
        usageQuota: {
          documents: Math.floor(Math.random() * 50),
          liveMinutes: Math.floor(Math.random() * 300),
          charactersTranslated: Math.floor(Math.random() * 10000),
        },
      },
    });

    console.log('✅ Created demo user:', user.email);
  }

  // Create construction terminology glossary
  const constructionGlossary = await prisma.glossary.upsert({
    where: { id: 'construction-terms-default' },
    update: {},
    create: {
      id: 'construction-terms-default',
      name: 'Construction Terms (Default)',
      description: 'Common construction terminology in Arabic and English',
      isShared: true,
      isActive: true,
      companyId: demoCompany.id,
      terms: [
        {
          id: '1',
          source: 'مقاول',
          target: 'contractor',
          context: 'construction',
          category: 'personnel',
          confidence: 1.0,
        },
        {
          id: '2',
          source: 'مهندس',
          target: 'engineer',
          context: 'construction',
          category: 'personnel',
          confidence: 1.0,
        },
        {
          id: '3',
          source: 'موقع البناء',
          target: 'construction site',
          context: 'construction',
          category: 'location',
          confidence: 1.0,
        },
        {
          id: '4',
          source: 'خرسانة',
          target: 'concrete',
          context: 'construction',
          category: 'material',
          confidence: 1.0,
        },
        {
          id: '5',
          source: 'حديد التسليح',
          target: 'reinforcement steel',
          context: 'construction',
          category: 'material',
          confidence: 1.0,
        },
        {
          id: '6',
          source: 'مخطط',
          target: 'blueprint',
          context: 'construction',
          category: 'document',
          confidence: 1.0,
        },
        {
          id: '7',
          source: 'رخصة البناء',
          target: 'building permit',
          context: 'construction',
          category: 'document',
          confidence: 1.0,
        },
        {
          id: '8',
          source: 'السلامة',
          target: 'safety',
          context: 'construction',
          category: 'safety',
          confidence: 1.0,
        },
        {
          id: '9',
          source: 'خوذة الأمان',
          target: 'safety helmet',
          context: 'construction',
          category: 'safety',
          confidence: 1.0,
        },
        {
          id: '10',
          source: 'جودة البناء',
          target: 'construction quality',
          context: 'construction',
          category: 'quality',
          confidence: 1.0,
        },
      ],
    },
  });

  console.log('✅ Created construction glossary:', constructionGlossary.name);

  // Create sample analytics data
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30); // 30 days ago

  const users = await prisma.user.findMany({
    where: { companyId: demoCompany.id },
  });

  for (const user of users) {
    for (let i = 0; i < 30; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);

      await prisma.userAnalytics.upsert({
        where: {
          userId_date: {
            userId: user.id,
            date,
          },
        },
        update: {},
        create: {
          userId: user.id,
          date,
          documentsProcessed: Math.floor(Math.random() * 10),
          charactersTranslated: Math.floor(Math.random() * 5000),
          liveMinutesUsed: Math.floor(Math.random() * 60),
          glossaryTermsUsed: Math.floor(Math.random() * 20),
          averageConfidence: 0.85 + Math.random() * 0.15,
          userSatisfaction: 4.0 + Math.random() * 1.0,
        },
      });
    }
  }

  console.log('✅ Created sample analytics data');

  // Create sample notifications
  for (const user of users) {
    await prisma.notification.createMany({
      data: [
        {
          userId: user.id,
          type: 'SYSTEM',
          title: 'Welcome to Tarjama.ai!',
          message: 'Thank you for joining our platform. Start by uploading your first document.',
          isRead: false,
        },
        {
          userId: user.id,
          type: 'FEATURE_ANNOUNCEMENT',
          title: 'New Feature: Live Translation',
          message: 'Try our new live meeting translation feature for real-time Arabic-English interpretation.',
          isRead: Math.random() > 0.5,
        },
      ],
    });
  }

  console.log('✅ Created sample notifications');

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Database seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
