#!/bin/bash

# Tarjama.ai Development Environment Setup Script
# This script sets up the development environment and starts all services

set -e

echo "🚀 Setting up Tarjama.ai Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 20+ and try again."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    
    print_success "All requirements are met!"
}

# Check if .env file exists
check_env_file() {
    print_status "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_success "Created .env file. Please review and update the values as needed."
    else
        print_success ".env file found!"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    npm install
    
    print_success "Dependencies installed!"
}

# Start Docker services
start_docker_services() {
    print_status "Starting Docker services (PostgreSQL, Redis, MinIO)..."
    
    # Stop any existing containers
    docker-compose down
    
    # Start the infrastructure services
    docker-compose up -d postgres redis minio
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps | grep -q "postgres.*Up"; then
        print_success "PostgreSQL is running!"
    else
        print_error "PostgreSQL failed to start"
        exit 1
    fi
    
    if docker-compose ps | grep -q "redis.*Up"; then
        print_success "Redis is running!"
    else
        print_error "Redis failed to start"
        exit 1
    fi
    
    if docker-compose ps | grep -q "minio.*Up"; then
        print_success "MinIO is running!"
    else
        print_error "MinIO failed to start"
        exit 1
    fi
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Wait a bit more for PostgreSQL to be fully ready
    sleep 5
    
    # Generate Prisma client
    cd apps/api
    npx prisma generate
    
    # Push database schema
    npx prisma db push
    
    # Seed the database
    npx prisma db seed
    
    cd ../..
    
    print_success "Database setup complete!"
}

# Create MinIO bucket
setup_minio() {
    print_status "Setting up MinIO bucket..."
    
    # Wait for MinIO to be ready
    sleep 5
    
    # Create bucket using MinIO client (if available) or curl
    docker-compose exec -T minio mc alias set local http://localhost:9000 minioadmin minioadmin123 || true
    docker-compose exec -T minio mc mb local/tarjama-dev-uploads || true
    
    print_success "MinIO bucket created!"
}

# Start development servers
start_dev_servers() {
    print_status "Starting development servers..."
    
    # Start the development servers in the background
    npm run dev &
    
    print_success "Development servers starting..."
    print_status "API will be available at: http://localhost:3001"
    print_status "Web app will be available at: http://localhost:3000"
    print_status "API documentation will be available at: http://localhost:3001/api/docs"
    print_status "MinIO console will be available at: http://localhost:9001"
    
    print_warning "Press Ctrl+C to stop all services"
    
    # Wait for user to stop
    wait
}

# Cleanup function
cleanup() {
    print_status "Stopping services..."
    docker-compose down
    print_success "All services stopped!"
}

# Set up cleanup on script exit
trap cleanup EXIT

# Main execution
main() {
    echo "🎯 Tarjama.ai Development Environment Setup"
    echo "=========================================="
    
    check_requirements
    check_env_file
    install_dependencies
    start_docker_services
    setup_database
    setup_minio
    start_dev_servers
}

# Run main function
main
