-- Initialize PostgreSQL database for Tarjama.ai
-- This script sets up the database with required extensions and configurations

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- <PERSON>reate custom types for better performance
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('USER', 'ADMIN', 'SUPER_ADMIN');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE plan_type AS ENUM ('FREE', 'PROFESSIONAL', 'TEAM', 'ENTERPRISE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE document_status AS ENUM ('PROCESSING', 'COMPLETED', 'FAILED', 'DELETED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE translation_provider AS ENUM ('OPENAI', 'DEEPL', 'GOOGLE', 'AZURE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE live_session_status AS ENUM ('ACTIVE', 'PAUSED', 'COMPLETED', 'FAILED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE notification_type AS ENUM (
        'SYSTEM', 
        'TRANSLATION_COMPLETE', 
        'QUOTA_WARNING', 
        'QUOTA_EXCEEDED', 
        'SUBSCRIPTION_EXPIRING', 
        'FEATURE_ANNOUNCEMENT'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance (will be created by Prisma migrations)
-- These are here for reference and manual optimization if needed

-- User table indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_company_id ON users(company_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Document table indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_user_id ON documents(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_status ON documents(status);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_created_at ON documents(created_at);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_language_detected ON documents(language_detected);

-- Translation table indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_user_id ON translations(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_provider ON translations(provider);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_created_at ON translations(created_at);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_source_language ON translations(source_language);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_target_language ON translations(target_language);

-- Full-text search indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_ocr_text_fts ON documents USING gin(to_tsvector('english', ocr_text));
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_source_text_fts ON translations USING gin(to_tsvector('arabic', source_text));
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_translated_text_fts ON translations USING gin(to_tsvector('english', translated_text));

-- Glossary table indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_glossaries_user_id ON glossaries(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_glossaries_company_id ON glossaries(company_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_glossaries_is_shared ON glossaries(is_shared);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_glossaries_is_active ON glossaries(is_active);

-- Analytics table indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_analytics_user_id ON user_analytics(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_analytics_date ON user_analytics(date);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_analytics_user_date ON user_analytics(user_id, date);

-- Live session table indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_sessions_user_id ON live_sessions(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_sessions_status ON live_sessions(status);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_sessions_started_at ON live_sessions(started_at);

-- Notification table indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_type ON notifications(type);

-- Create functions for common operations
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create function to calculate user usage statistics
CREATE OR REPLACE FUNCTION calculate_user_usage_stats(user_id_param UUID, start_date DATE, end_date DATE)
RETURNS TABLE(
    total_documents INTEGER,
    total_characters BIGINT,
    total_live_minutes INTEGER,
    avg_confidence NUMERIC,
    avg_satisfaction NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(documents_processed), 0)::INTEGER as total_documents,
        COALESCE(SUM(characters_translated), 0)::BIGINT as total_characters,
        COALESCE(SUM(live_minutes_used), 0)::INTEGER as total_live_minutes,
        COALESCE(AVG(average_confidence), 0)::NUMERIC as avg_confidence,
        COALESCE(AVG(user_satisfaction), 0)::NUMERIC as avg_satisfaction
    FROM user_analytics 
    WHERE user_id = user_id_param 
    AND date BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- Create function to get user quota usage
CREATE OR REPLACE FUNCTION get_user_quota_usage(user_id_param UUID)
RETURNS TABLE(
    current_documents INTEGER,
    current_live_minutes INTEGER,
    current_characters BIGINT,
    plan_limits JSONB
) AS $$
DECLARE
    user_plan plan_type;
    user_quota JSONB;
BEGIN
    SELECT plan_type, usage_quota INTO user_plan, user_quota
    FROM users WHERE id = user_id_param;
    
    RETURN QUERY
    SELECT 
        COALESCE((user_quota->>'documents')::INTEGER, 0) as current_documents,
        COALESCE((user_quota->>'liveMinutes')::INTEGER, 0) as current_live_minutes,
        COALESCE((user_quota->>'charactersTranslated')::BIGINT, 0) as current_characters,
        CASE user_plan
            WHEN 'FREE' THEN '{"documents": 10, "liveMinutes": 30, "charactersTranslated": 5000}'::JSONB
            WHEN 'PROFESSIONAL' THEN '{"documents": 500, "liveMinutes": 600, "charactersTranslated": 250000}'::JSONB
            WHEN 'TEAM' THEN '{"documents": 2000, "liveMinutes": 3000, "charactersTranslated": 1000000}'::JSONB
            WHEN 'ENTERPRISE' THEN '{"documents": -1, "liveMinutes": -1, "charactersTranslated": -1}'::JSONB
            ELSE '{"documents": 0, "liveMinutes": 0, "charactersTranslated": 0}'::JSONB
        END as plan_limits;
END;
$$ LANGUAGE plpgsql;

-- Create function to search glossary terms
CREATE OR REPLACE FUNCTION search_glossary_terms(
    user_id_param UUID,
    search_term TEXT,
    source_lang TEXT DEFAULT 'ar',
    target_lang TEXT DEFAULT 'en'
)
RETURNS TABLE(
    glossary_id TEXT,
    glossary_name TEXT,
    term_id TEXT,
    source_text TEXT,
    target_text TEXT,
    context TEXT,
    confidence NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id as glossary_id,
        g.name as glossary_name,
        (term->>'id')::TEXT as term_id,
        (term->>'source')::TEXT as source_text,
        (term->>'target')::TEXT as target_text,
        (term->>'context')::TEXT as context,
        COALESCE((term->>'confidence')::NUMERIC, 1.0) as confidence
    FROM glossaries g,
         jsonb_array_elements(g.terms) as term
    WHERE (g.user_id = user_id_param OR g.is_shared = true)
    AND g.is_active = true
    AND (
        (term->>'source')::TEXT ILIKE '%' || search_term || '%' OR
        (term->>'target')::TEXT ILIKE '%' || search_term || '%'
    )
    ORDER BY 
        CASE 
            WHEN (term->>'source')::TEXT = search_term THEN 1
            WHEN (term->>'target')::TEXT = search_term THEN 1
            WHEN (term->>'source')::TEXT ILIKE search_term || '%' THEN 2
            WHEN (term->>'target')::TEXT ILIKE search_term || '%' THEN 2
            ELSE 3
        END,
        confidence DESC;
END;
$$ LANGUAGE plpgsql;

-- Set up database configuration for optimal performance
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET log_min_duration_statement = 1000;
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
ALTER SYSTEM SET log_lock_waits = on;

-- Note: These settings require a PostgreSQL restart to take effect
-- In production, these should be set in postgresql.conf

COMMENT ON DATABASE tarjama_dev IS 'Tarjama.ai Development Database - AI-powered Arabic-English translation platform';

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT CONNECT ON DATABASE tarjama_dev TO tarjama_user;
-- GRANT USAGE ON SCHEMA public TO tarjama_user;
-- GRANT CREATE ON SCHEMA public TO tarjama_user;
