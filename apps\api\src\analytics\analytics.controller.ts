import {
  Controller,
  Get,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalyticsService } from './analytics.service';

@ApiTags('analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('overview')
  @ApiOperation({ 
    summary: 'Get analytics overview',
    description: 'Get comprehensive analytics overview for the current user'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Analytics overview retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalUsers: { type: 'number' },
        totalDocuments: { type: 'number' },
        totalTranslations: { type: 'number' },
        totalGlossaries: { type: 'number' },
        totalLiveSessions: { type: 'number' },
        charactersTranslated: { type: 'number' },
        averageConfidence: { type: 'number' },
        topLanguagePairs: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              sourceLanguage: { type: 'string' },
              targetLanguage: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getOverview(@Request() req: any) {
    return await this.analyticsService.getOverview(req.user.id);
  }

  @Get('user')
  @ApiOperation({ 
    summary: 'Get user analytics',
    description: 'Get detailed analytics for the current user over a specified period'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'User analytics retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          userId: { type: 'string' },
          period: { type: 'string' },
          documentsProcessed: { type: 'number' },
          charactersTranslated: { type: 'number' },
          liveMinutesUsed: { type: 'number' },
          glossaryTermsUsed: { type: 'number' },
          averageConfidence: { type: 'number' },
          userSatisfaction: { type: 'number' },
        },
      },
    },
  })
  @ApiQuery({ 
    name: 'days', 
    required: false, 
    type: Number, 
    description: 'Number of days to look back (default: 30)' 
  })
  async getUserAnalytics(
    @Request() req: any,
    @Query('days', new DefaultValuePipe(30), ParseIntPipe) days: number,
  ) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return await this.analyticsService.getUserAnalytics(
      req.user.id,
      startDate,
      endDate,
    );
  }

  @Get('usage')
  @ApiOperation({ 
    summary: 'Get usage metrics',
    description: 'Get detailed usage metrics including daily, weekly, and monthly breakdowns'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Usage metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        daily: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string' },
              users: { type: 'number' },
              documents: { type: 'number' },
              translations: { type: 'number' },
              characters: { type: 'number' },
            },
          },
        },
        weekly: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              week: { type: 'string' },
              users: { type: 'number' },
              documents: { type: 'number' },
              translations: { type: 'number' },
              characters: { type: 'number' },
            },
          },
        },
        monthly: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string' },
              users: { type: 'number' },
              documents: { type: 'number' },
              translations: { type: 'number' },
              characters: { type: 'number' },
            },
          },
        },
      },
    },
  })
  @ApiQuery({ 
    name: 'days', 
    required: false, 
    type: Number, 
    description: 'Number of days to include in metrics (default: 30)' 
  })
  async getUsageMetrics(
    @Request() req: any,
    @Query('days', new DefaultValuePipe(30), ParseIntPipe) days: number,
  ) {
    return await this.analyticsService.getUsageMetrics(req.user.id, days);
  }

  @Get('dashboard')
  @ApiOperation({ 
    summary: 'Get dashboard data',
    description: 'Get combined analytics data for dashboard display'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Dashboard data retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        overview: { type: 'object' },
        recentActivity: { type: 'array' },
        usageMetrics: { type: 'object' },
      },
    },
  })
  async getDashboardData(@Request() req: any) {
    const [overview, recentActivity, usageMetrics] = await Promise.all([
      this.analyticsService.getOverview(req.user.id),
      this.analyticsService.getUserAnalytics(
        req.user.id,
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        new Date(),
      ),
      this.analyticsService.getUsageMetrics(req.user.id, 30),
    ]);

    return {
      overview,
      recentActivity,
      usageMetrics,
    };
  }
}
