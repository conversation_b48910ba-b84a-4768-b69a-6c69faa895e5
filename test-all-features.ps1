# Tarjama.ai Complete Feature Test Script
# This script tests all major functionalities of the application

Write-Host "🚀 Starting Tarjama.ai Complete Feature Test" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

$baseUrl = "http://localhost:3000"
$testResults = @()

function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description
    )
    
    try {
        Write-Host "Testing: $Description" -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 10
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ PASS: $Description" -ForegroundColor Green
            $testResults += @{
                Test = $Description
                Status = "PASS"
                StatusCode = $response.StatusCode
                Url = $Url
            }
        } else {
            Write-Host "❌ FAIL: $Description (Status: $($response.StatusCode))" -ForegroundColor Red
            $testResults += @{
                Test = $Description
                Status = "FAIL"
                StatusCode = $response.StatusCode
                Url = $Url
            }
        }
    }
    catch {
        Write-Host "❌ ERROR: $Description - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{
            Test = $Description
            Status = "ERROR"
            StatusCode = "N/A"
            Url = $Url
            Error = $_.Exception.Message
        }
    }
}

Write-Host "`n📱 Testing Marketing Pages..." -ForegroundColor Cyan

# Test Marketing Pages
Test-Endpoint "$baseUrl/" "Homepage"
Test-Endpoint "$baseUrl/features" "Features Page"
Test-Endpoint "$baseUrl/pricing" "Pricing Page"
Test-Endpoint "$baseUrl/about" "About Page"
Test-Endpoint "$baseUrl/contact" "Contact Page"

Write-Host "`n🔐 Testing Authentication Pages..." -ForegroundColor Cyan

# Test Authentication Pages
Test-Endpoint "$baseUrl/login" "Login Page"
Test-Endpoint "$baseUrl/register" "Register Page"
Test-Endpoint "$baseUrl/forgot-password" "Forgot Password Page"

Write-Host "`n🧪 Testing Demo & Translation..." -ForegroundColor Cyan

# Test Demo Page
Test-Endpoint "$baseUrl/demo" "Translation Demo Page"

Write-Host "`n📊 Testing Dashboard Pages..." -ForegroundColor Cyan

# Test Dashboard Pages (these will redirect to login if not authenticated, but should load)
Test-Endpoint "$baseUrl/dashboard/overview" "Dashboard Overview"
Test-Endpoint "$baseUrl/dashboard/translations" "Translations Page"
Test-Endpoint "$baseUrl/dashboard/documents" "Documents Page"
Test-Endpoint "$baseUrl/dashboard/glossaries" "Glossaries Page"
Test-Endpoint "$baseUrl/dashboard/live-sessions" "Live Sessions Page"
Test-Endpoint "$baseUrl/dashboard/analytics" "Analytics Page"
Test-Endpoint "$baseUrl/dashboard/settings" "Settings Page"
Test-Endpoint "$baseUrl/dashboard/profile" "Profile Page"

Write-Host "`n📈 Generating Test Report..." -ForegroundColor Cyan

# Generate Summary
$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
$failedTests = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
$errorTests = ($testResults | Where-Object { $_.Status -eq "ERROR" }).Count

Write-Host "`n🎯 TEST SUMMARY" -ForegroundColor Magenta
Write-Host "===============" -ForegroundColor Magenta
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "✅ Passed: $passedTests" -ForegroundColor Green
Write-Host "❌ Failed: $failedTests" -ForegroundColor Red
Write-Host "⚠️  Errors: $errorTests" -ForegroundColor Yellow

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 ALL TESTS PASSED! Tarjama.ai is fully functional!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some tests failed. Check the details above." -ForegroundColor Yellow
}

Write-Host "`n📋 DETAILED RESULTS:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

foreach ($result in $testResults) {
    $statusColor = switch ($result.Status) {
        "PASS" { "Green" }
        "FAIL" { "Red" }
        "ERROR" { "Yellow" }
    }
    
    Write-Host "$($result.Status): $($result.Test)" -ForegroundColor $statusColor
    Write-Host "   URL: $($result.Url)" -ForegroundColor Gray
    if ($result.StatusCode) {
        Write-Host "   Status Code: $($result.StatusCode)" -ForegroundColor Gray
    }
    if ($result.Error) {
        Write-Host "   Error: $($result.Error)" -ForegroundColor Gray
    }
    Write-Host ""
}

Write-Host "🔗 DEMO CREDENTIALS FOR TESTING:" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host "Email: <EMAIL>" -ForegroundColor White
Write-Host "Password: demo123" -ForegroundColor White

Write-Host "`n🚀 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan
Write-Host "1. Visit http://localhost:3000 to see the homepage" -ForegroundColor White
Write-Host "2. Try the demo at http://localhost:3000/demo" -ForegroundColor White
Write-Host "3. Login with demo credentials at http://localhost:3000/login" -ForegroundColor White
Write-Host "4. Explore the dashboard at http://localhost:3000/dashboard/overview" -ForegroundColor White
Write-Host "5. Test file upload at http://localhost:3000/dashboard/documents" -ForegroundColor White

Write-Host "`n✨ Test completed! Tarjama.ai is ready for use!" -ForegroundColor Green
