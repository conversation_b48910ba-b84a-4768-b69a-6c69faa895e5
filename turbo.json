{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["^build"], "outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}}}