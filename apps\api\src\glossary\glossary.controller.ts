import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  GlossaryService,
  CreateGlossaryDto,
  UpdateGlossaryDto,
  ImportGlossaryDto,
  GlossaryTerm,
} from './glossary.service';
import { IsString, IsOptional, IsBoolean, IsArray, ValidateNested, IsIn, Length } from 'class-validator';
import { Type } from 'class-transformer';

export class GlossaryTermDto {
  @IsString()
  @Length(1, 500)
  source: string;

  @IsString()
  @Length(1, 500)
  target: string;

  @IsOptional()
  @IsString()
  @Length(0, 200)
  context?: string;

  @IsOptional()
  @IsString()
  @Length(0, 100)
  category?: string;
}

export class CreateGlossaryRequestDto {
  @IsString()
  @Length(1, 200)
  name: string;

  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string;

  @IsString()
  @IsIn(['ar', 'en'])
  sourceLanguage: string;

  @IsString()
  @IsIn(['ar', 'en'])
  targetLanguage: string;

  @IsOptional()
  @IsBoolean()
  isShared?: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GlossaryTermDto)
  terms?: GlossaryTermDto[];
}

export class UpdateGlossaryRequestDto {
  @IsOptional()
  @IsString()
  @Length(1, 200)
  name?: string;

  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string;

  @IsOptional()
  @IsBoolean()
  isShared?: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GlossaryTermDto)
  terms?: GlossaryTermDto[];
}

export class AddTermsRequestDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GlossaryTermDto)
  terms: GlossaryTermDto[];
}

@ApiTags('glossaries')
@Controller('glossaries')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class GlossaryController {
  constructor(private readonly glossaryService: GlossaryService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Create new glossary',
    description: 'Create a new glossary with optional initial terms'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Glossary created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiBody({ type: CreateGlossaryRequestDto })
  async createGlossary(
    @Request() req: any,
    @Body(ValidationPipe) createDto: CreateGlossaryRequestDto,
  ) {
    return await this.glossaryService.createGlossary(req.user.id, createDto);
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get user glossaries',
    description: 'Retrieve paginated list of user glossaries including shared ones'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Glossaries retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ name: 'includeShared', required: false, type: Boolean, description: 'Include shared glossaries (default: true)' })
  async getGlossaries(
    @Request() req: any,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('includeShared') includeShared: string = 'true',
  ) {
    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 20));
    const includeSharedBool = includeShared.toLowerCase() !== 'false';
    
    return await this.glossaryService.getGlossaries(
      req.user.id,
      pageNum,
      limitNum,
      includeSharedBool,
    );
  }

  @Get('search')
  @ApiOperation({ 
    summary: 'Search glossary terms',
    description: 'Search for terms across all accessible glossaries'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Search results retrieved successfully',
  })
  @ApiQuery({ name: 'q', required: true, type: String, description: 'Search query' })
  @ApiQuery({ name: 'sourceLanguage', required: false, enum: ['ar', 'en'], description: 'Filter by source language' })
  @ApiQuery({ name: 'targetLanguage', required: false, enum: ['ar', 'en'], description: 'Filter by target language' })
  async searchTerms(
    @Request() req: any,
    @Query('q') query: string,
    @Query('sourceLanguage') sourceLanguage?: string,
    @Query('targetLanguage') targetLanguage?: string,
  ) {
    return await this.glossaryService.searchTerms(
      req.user.id,
      query,
      sourceLanguage,
      targetLanguage,
    );
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get glossary by ID',
    description: 'Retrieve a specific glossary with all its terms'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Glossary retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Glossary not found' })
  @ApiParam({ name: 'id', description: 'Glossary ID' })
  async getGlossaryById(
    @Request() req: any,
    @Param('id') glossaryId: string,
  ) {
    return await this.glossaryService.getGlossaryById(req.user.id, glossaryId);
  }

  @Put(':id')
  @ApiOperation({ 
    summary: 'Update glossary',
    description: 'Update glossary details and terms (only for owned glossaries)'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Glossary updated successfully',
  })
  @ApiResponse({ status: 403, description: 'Cannot update glossary owned by another user' })
  @ApiResponse({ status: 404, description: 'Glossary not found' })
  @ApiParam({ name: 'id', description: 'Glossary ID' })
  @ApiBody({ type: UpdateGlossaryRequestDto })
  async updateGlossary(
    @Request() req: any,
    @Param('id') glossaryId: string,
    @Body(ValidationPipe) updateDto: UpdateGlossaryRequestDto,
  ) {
    return await this.glossaryService.updateGlossary(req.user.id, glossaryId, updateDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete glossary',
    description: 'Delete a glossary (only for owned glossaries)'
  })
  @ApiResponse({ status: 204, description: 'Glossary deleted successfully' })
  @ApiResponse({ status: 403, description: 'Cannot delete glossary owned by another user' })
  @ApiResponse({ status: 404, description: 'Glossary not found' })
  @ApiParam({ name: 'id', description: 'Glossary ID' })
  async deleteGlossary(
    @Request() req: any,
    @Param('id') glossaryId: string,
  ) {
    await this.glossaryService.deleteGlossary(req.user.id, glossaryId);
  }

  @Post(':id/terms')
  @ApiOperation({ 
    summary: 'Add terms to glossary',
    description: 'Add new terms to an existing glossary'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Terms added successfully',
  })
  @ApiResponse({ status: 403, description: 'Cannot modify glossary owned by another user' })
  @ApiResponse({ status: 404, description: 'Glossary not found' })
  @ApiParam({ name: 'id', description: 'Glossary ID' })
  @ApiBody({ type: AddTermsRequestDto })
  async addTerms(
    @Request() req: any,
    @Param('id') glossaryId: string,
    @Body(ValidationPipe) addTermsDto: AddTermsRequestDto,
  ) {
    return await this.glossaryService.addTerms(req.user.id, glossaryId, addTermsDto.terms);
  }

  @Post('import')
  @ApiOperation({ 
    summary: 'Import glossary',
    description: 'Import a glossary with terms from external source'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Glossary imported successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid import data' })
  async importGlossary(
    @Request() req: any,
    @Body(ValidationPipe) importDto: ImportGlossaryDto,
  ) {
    return await this.glossaryService.importGlossary(req.user.id, importDto);
  }
}
