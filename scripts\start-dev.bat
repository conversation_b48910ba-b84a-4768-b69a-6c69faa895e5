@echo off
echo 🚀 Starting Tarjama.ai Development Environment...

REM Copy environment file to API directory if it doesn't exist
if not exist "apps\api\.env" (
    echo [INFO] Copying environment file to API directory...
    copy .env apps\api\.env
)

REM Start Docker services
echo [INFO] Starting Docker services...
docker-compose up -d postgres redis minio

REM Wait for services
echo [INFO] Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Setup database
echo [INFO] Setting up database...
cd apps\api
call npx prisma generate
call npx prisma db push
call npx prisma db seed
cd ..\..

REM Start development servers
echo [INFO] Starting development servers...
echo [SUCCESS] Services will be available at:
echo   - Web App: http://localhost:3000
echo   - API: http://localhost:3001
echo   - API Docs: http://localhost:3001/api/docs
echo   - MinIO Console: http://localhost:9001
echo.
echo [INFO] Press Ctrl+C to stop all services
echo.

start /B npm run dev

REM Keep the window open
pause
