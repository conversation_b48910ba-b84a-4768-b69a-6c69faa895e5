import { Controller, Get, HttpStatus, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get application health status',
    description: 'Returns comprehensive health information including service status and metrics'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Health status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['healthy', 'unhealthy', 'degraded'] },
        timestamp: { type: 'string', format: 'date-time' },
        uptime: { type: 'number' },
        version: { type: 'string' },
        environment: { type: 'string' },
        services: {
          type: 'object',
          properties: {
            database: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['healthy', 'unhealthy'] },
                responseTime: { type: 'number' },
                error: { type: 'string' },
              },
            },
            redis: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['healthy', 'unhealthy'] },
                responseTime: { type: 'number' },
                error: { type: 'string' },
              },
            },
            storage: {
              type: 'object',
              properties: {
                status: { type: 'string', enum: ['healthy', 'unhealthy'] },
                responseTime: { type: 'number' },
                error: { type: 'string' },
              },
            },
          },
        },
        metrics: {
          type: 'object',
          properties: {
            memoryUsage: { type: 'object' },
            cpuUsage: { type: 'object' },
          },
        },
      },
    },
  })
  async getHealth() {
    return await this.healthService.getHealthStatus();
  }

  @Get('ready')
  @ApiOperation({ 
    summary: 'Get readiness status',
    description: 'Returns whether the application is ready to serve requests (Kubernetes readiness probe)'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application is ready',
    schema: {
      type: 'object',
      properties: {
        ready: { type: 'boolean' },
        services: { type: 'object' },
      },
    },
  })
  @ApiResponse({ 
    status: 503, 
    description: 'Application is not ready',
  })
  async getReadiness() {
    const result = await this.healthService.getReadinessStatus();
    
    if (!result.ready) {
      // Return 503 Service Unavailable if not ready
      throw new Error('Service not ready');
    }
    
    return result;
  }

  @Get('live')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get liveness status',
    description: 'Returns whether the application is alive (Kubernetes liveness probe)'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application is alive',
    schema: {
      type: 'object',
      properties: {
        alive: { type: 'boolean' },
        uptime: { type: 'number' },
      },
    },
  })
  async getLiveness() {
    return await this.healthService.getLivenessStatus();
  }

  @Get('metrics')
  @ApiOperation({ 
    summary: 'Get system metrics',
    description: 'Returns detailed system metrics including memory, CPU, and database statistics'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'System metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        uptime: { type: 'number' },
        memory: {
          type: 'object',
          properties: {
            rss: { type: 'number' },
            heapTotal: { type: 'number' },
            heapUsed: { type: 'number' },
            external: { type: 'number' },
            arrayBuffers: { type: 'number' },
          },
        },
        cpu: {
          type: 'object',
          properties: {
            user: { type: 'number' },
            system: { type: 'number' },
          },
        },
        database: {
          type: 'object',
          properties: {
            users: { type: 'number' },
            documents: { type: 'number' },
            translations: { type: 'number' },
            glossaries: { type: 'number' },
          },
        },
      },
    },
  })
  async getMetrics() {
    return await this.healthService.getSystemMetrics();
  }
}
