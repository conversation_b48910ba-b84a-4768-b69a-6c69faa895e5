# Tarjama.ai - Infrastructure & DevOps Setup

## 1. Infrastructure Architecture Overview

### Multi-Region Deployment Strategy
```
┌─────────────────────────────────────────────────────────────┐
│                    PRODUCTION ARCHITECTURE                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐         ┌─────────────────┐           │
│  │   PRIMARY REGION │         │  SECONDARY REGION│           │
│  │  (me-south-1)   │         │  (me-central-1)  │           │
│  │                 │         │                  │           │
│  │ ┌─────────────┐ │         │ ┌─────────────┐  │           │
│  │ │ EKS Cluster │ │◄────────┤ │ EKS Cluster │  │           │
│  │ │ (3 AZs)     │ │         │ │ (3 AZs)     │  │           │
│  │ └─────────────┘ │         │ └─────────────┘  │           │
│  │                 │         │                  │           │
│  │ ┌─────────────┐ │         │ ┌─────────────┐  │           │
│  │ │ RDS Primary │ │────────►│ │ RDS Replica │  │           │
│  │ │ (Multi-AZ)  │ │         │ │ (Read-only) │  │           │
│  │ └─────────────┘ │         │ └─────────────┘  │           │
│  │                 │         │                  │           │
│  │ ┌─────────────┐ │         │ ┌─────────────┐  │           │
│  │ │ Redis       │ │◄────────┤ │ Redis       │  │           │
│  │ │ Cluster     │ │         │ │ Replica     │  │           │
│  │ └─────────────┘ │         │ └─────────────┘  │           │
│  └─────────────────┘         └─────────────────┘           │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┤
│  │              GLOBAL SERVICES                            │
│  ├─────────────────────────────────────────────────────────┤
│  │ CloudFront CDN │ Route 53 DNS │ WAF │ S3 Cross-Region  │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

### Environment Strategy
- **Development**: Single region, minimal resources, auto-shutdown
- **Staging**: Single region, production-like, continuous deployment
- **Production**: Multi-region, high availability, manual deployment approval

## 2. Terraform Infrastructure as Code

### Directory Structure
```
infrastructure/
├── environments/
│   ├── dev/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── terraform.tfvars
│   ├── staging/
│   └── production/
├── modules/
│   ├── eks-cluster/
│   ├── rds-postgres/
│   ├── redis-cluster/
│   ├── s3-buckets/
│   ├── cloudfront/
│   └── monitoring/
└── shared/
    ├── backend.tf
    ├── providers.tf
    └── variables.tf
```

### Core Terraform Modules

#### EKS Cluster Module
```hcl
# modules/eks-cluster/main.tf
resource "aws_eks_cluster" "main" {
  name     = var.cluster_name
  role_arn = aws_iam_role.cluster.arn
  version  = var.kubernetes_version

  vpc_config {
    subnet_ids              = var.subnet_ids
    endpoint_private_access = true
    endpoint_public_access  = true
    public_access_cidrs     = var.allowed_cidrs
  }

  encryption_config {
    provider {
      key_arn = aws_kms_key.eks.arn
    }
    resources = ["secrets"]
  }

  enabled_cluster_log_types = [
    "api", "audit", "authenticator", "controllerManager", "scheduler"
  ]

  depends_on = [
    aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy,
    aws_cloudwatch_log_group.cluster
  ]

  tags = var.tags
}

resource "aws_eks_node_group" "main" {
  cluster_name    = aws_eks_cluster.main.name
  node_group_name = "${var.cluster_name}-nodes"
  node_role_arn   = aws_iam_role.node.arn
  subnet_ids      = var.private_subnet_ids

  scaling_config {
    desired_size = var.node_desired_size
    max_size     = var.node_max_size
    min_size     = var.node_min_size
  }

  update_config {
    max_unavailable = 1
  }

  instance_types = var.node_instance_types
  capacity_type  = "ON_DEMAND"
  disk_size      = var.node_disk_size

  remote_access {
    ec2_ssh_key = var.ssh_key_name
  }

  tags = var.tags
}
```

#### RDS PostgreSQL Module
```hcl
# modules/rds-postgres/main.tf
resource "aws_db_instance" "main" {
  identifier = var.db_identifier
  
  engine         = "postgres"
  engine_version = var.postgres_version
  instance_class = var.instance_class
  
  allocated_storage     = var.allocated_storage
  max_allocated_storage = var.max_allocated_storage
  storage_type          = "gp3"
  storage_encrypted     = true
  kms_key_id           = aws_kms_key.rds.arn
  
  db_name  = var.database_name
  username = var.master_username
  password = var.master_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = var.backup_retention_period
  backup_window          = var.backup_window
  maintenance_window     = var.maintenance_window
  
  multi_az               = var.multi_az
  publicly_accessible    = false
  
  enabled_cloudwatch_logs_exports = ["postgresql"]
  performance_insights_enabled    = true
  monitoring_interval            = 60
  monitoring_role_arn           = aws_iam_role.rds_monitoring.arn
  
  deletion_protection = var.deletion_protection
  skip_final_snapshot = var.skip_final_snapshot
  
  tags = var.tags
}

resource "aws_db_subnet_group" "main" {
  name       = "${var.db_identifier}-subnet-group"
  subnet_ids = var.subnet_ids
  
  tags = merge(var.tags, {
    Name = "${var.db_identifier}-subnet-group"
  })
}
```

### Environment Configuration
```hcl
# environments/production/terraform.tfvars
region = "me-south-1"
environment = "production"

# EKS Configuration
cluster_name = "tarjama-prod"
kubernetes_version = "1.28"
node_instance_types = ["m5.large", "m5.xlarge"]
node_desired_size = 3
node_min_size = 3
node_max_size = 10

# RDS Configuration
db_identifier = "tarjama-prod-db"
postgres_version = "16.1"
instance_class = "db.r6g.large"
allocated_storage = 100
max_allocated_storage = 1000
multi_az = true
backup_retention_period = 30

# Redis Configuration
redis_node_type = "cache.r6g.large"
redis_num_cache_nodes = 3
redis_parameter_group_name = "default.redis7"

# S3 Configuration
s3_bucket_name = "tarjama-prod-storage"
s3_versioning_enabled = true
s3_lifecycle_enabled = true

tags = {
  Environment = "production"
  Project     = "tarjama"
  Owner       = "devops"
  CostCenter  = "engineering"
}
```

## 3. Kubernetes Manifests

### Namespace and Resource Quotas
```yaml
# k8s/namespaces/tarjama-prod.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tarjama-prod
  labels:
    name: tarjama-prod
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: compute-quota
  namespace: tarjama-prod
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services.loadbalancers: "3"
```

### Application Deployment
```yaml
# k8s/apps/api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tarjama-api
  namespace: tarjama-prod
  labels:
    app: tarjama-api
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tarjama-api
  template:
    metadata:
      labels:
        app: tarjama-api
        version: v1
    spec:
      containers:
      - name: api
        image: tarjama/api:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      imagePullSecrets:
      - name: registry-secret
```

### Service and Ingress
```yaml
# k8s/services/api-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tarjama-api-service
  namespace: tarjama-prod
spec:
  selector:
    app: tarjama-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tarjama-api-ingress
  namespace: tarjama-prod
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.tarjama.ai
    secretName: api-tls-secret
  rules:
  - host: api.tarjama.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tarjama-api-service
            port:
              number: 80
```

### Horizontal Pod Autoscaler
```yaml
# k8s/autoscaling/api-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tarjama-api-hpa
  namespace: tarjama-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tarjama-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

## 4. CI/CD Pipeline Configuration

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  AWS_REGION: me-south-1
  EKS_CLUSTER_NAME: tarjama-prod
  ECR_REPOSITORY: tarjama/api

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test:ci
    
    - name: Run security audit
      run: npm audit --audit-level high
    
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
    
    - name: Build and push Docker image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region $AWS_REGION --name $EKS_CLUSTER_NAME
    
    - name: Deploy to Kubernetes
      env:
        IMAGE_TAG: ${{ github.sha }}
      run: |
        sed -i "s|tarjama/api:latest|${{ steps.login-ecr.outputs.registry }}/$ECR_REPOSITORY:$IMAGE_TAG|g" k8s/apps/api-deployment.yaml
        kubectl apply -f k8s/
        kubectl rollout status deployment/tarjama-api -n tarjama-prod --timeout=300s
    
    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app=tarjama-api -n tarjama-prod --timeout=300s
        curl -f https://api.tarjama.ai/health || exit 1
```

### Docker Configuration
```dockerfile
# Dockerfile
FROM node:20-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:20-alpine AS runtime

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

WORKDIR /app

# Copy application files
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .

# Security: Remove unnecessary packages
RUN apk del apk-tools

USER nextjs

EXPOSE 3000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1
```

## 5. Monitoring and Observability

### Prometheus Configuration
```yaml
# monitoring/prometheus-config.yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert-rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  - job_name: 'tarjama-api'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_app]
      action: keep
      regex: tarjama-api
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__
```

### Alert Rules
```yaml
# monitoring/alert-rules.yml
groups:
- name: tarjama-api
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High latency detected"
      description: "95th percentile latency is {{ $value }} seconds"

  - alert: PodCrashLooping
    expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Pod is crash looping"
      description: "Pod {{ $labels.pod }} is restarting frequently"
```

### Grafana Dashboard Configuration
```json
{
  "dashboard": {
    "title": "Tarjama.ai Production Dashboard",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "singlestat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      }
    ]
  }
}
```

## Next Steps
1. Set up AWS accounts and configure IAM roles
2. Deploy Terraform infrastructure for development environment
3. Configure CI/CD pipeline and test deployments
4. Set up monitoring and alerting systems
5. Create disaster recovery and backup procedures
