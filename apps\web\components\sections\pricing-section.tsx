import { CheckIcon } from '@heroicons/react/24/outline';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const tiers = [
  {
    name: 'Free',
    id: 'tier-free',
    href: '/register',
    priceMonthly: '$0',
    description: 'Perfect for trying out Tarjama.ai',
    features: [
      '10 pages/month translation',
      '30 minutes/month live translation',
      'Basic OCR and translation',
      'Community support',
    ],
    featured: false,
  },
  {
    name: 'Professional',
    id: 'tier-professional',
    href: '/register',
    priceMonthly: '$29',
    description: 'Best for individual professionals',
    features: [
      '500 pages/month translation',
      '10 hours/month live translation',
      'Advanced AI features',
      'Email support',
      'Mobile app access',
      'Personal glossaries',
    ],
    featured: true,
  },
  {
    name: 'Team',
    id: 'tier-team',
    href: '/contact',
    priceMonthly: '$99',
    description: 'Perfect for small teams',
    features: [
      '2,000 pages/month translation',
      '50 hours/month live translation',
      'Team collaboration',
      'Shared glossaries',
      'Priority support',
      'Admin dashboard',
      'Usage analytics',
    ],
    featured: false,
  },
];

export function PricingSection() {
  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Simple, transparent pricing
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Choose the plan that fits your translation needs. Start free, upgrade anytime.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-lg grid-cols-1 gap-8 lg:max-w-4xl lg:grid-cols-3">
          {tiers.map((tier) => (
            <Card key={tier.id} className={tier.featured ? 'ring-2 ring-primary' : ''}>
              <CardHeader>
                <CardTitle className="text-lg font-semibold leading-8">
                  {tier.name}
                </CardTitle>
                <CardDescription>{tier.description}</CardDescription>
                <p className="mt-6 flex items-baseline gap-x-1">
                  <span className="text-4xl font-bold tracking-tight">
                    {tier.priceMonthly}
                  </span>
                  <span className="text-sm font-semibold leading-6 text-muted-foreground">
                    /month
                  </span>
                </p>
              </CardHeader>
              <CardContent>
                <Button 
                  className="w-full" 
                  variant={tier.featured ? 'default' : 'outline'}
                  asChild
                >
                  <a href={tier.href}>
                    {tier.name === 'Free' ? 'Get started' : 'Start trial'}
                  </a>
                </Button>
                <ul role="list" className="mt-8 space-y-3 text-sm leading-6">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex gap-x-3">
                      <CheckIcon className="h-6 w-5 flex-none text-primary" aria-hidden="true" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
