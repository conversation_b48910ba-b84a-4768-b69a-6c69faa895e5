"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";

export default function DashboardOverviewPage() {
  const [user] = useState({
    name: "<PERSON>",
    company: "Dubai Construction Co.",
    plan: "Professional",
    email: "<EMAIL>",
  });

  const [stats] = useState({
    documentsTranslated: 156,
    charactersTranslated: 45230,
    liveSessionsUsed: 23,
    glossariesCreated: 8,
    monthlyUsage: 75, // percentage
    planLimit: 100000, // characters per month
  });

  const [recentActivity] = useState([
    {
      id: 1,
      type: "document",
      title: "Construction Contract - Phase 2",
      status: "completed",
      time: "2 hours ago",
      size: "2.4 MB",
    },
    {
      id: 2,
      type: "live",
      title: "Site Meeting Translation",
      status: "completed",
      time: "5 hours ago",
      duration: "45 min",
    },
    {
      id: 3,
      type: "glossary",
      title: "Updated HVAC Terminology",
      status: "completed",
      time: "1 day ago",
      terms: "127 terms",
    },
    {
      id: 4,
      type: "document",
      title: "Safety Manual Translation",
      status: "processing",
      time: "2 days ago",
      size: "5.1 MB",
    },
  ]);

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="border-b border-gray-800 bg-black/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="text-2xl font-bold text-white hover:text-gray-300 transition-colors">
              Tarjama.ai
            </Link>
            <div className="flex items-center space-x-6">
              <nav className="hidden md:flex space-x-6">
                <Link href="/dashboard/overview" className="text-white font-medium">Overview</Link>
                <Link href="/dashboard/translations" className="text-gray-400 hover:text-white transition-colors">Translations</Link>
                <Link href="/dashboard/glossaries" className="text-gray-400 hover:text-white transition-colors">Glossaries</Link>
                <Link href="/dashboard/settings" className="text-gray-400 hover:text-white transition-colors">Settings</Link>
              </nav>
              <div className="flex items-center space-x-3">
                <span className="text-gray-300 text-sm">Welcome back, {user.name}</span>
                <div className="w-8 h-8 bg-white text-black rounded-full flex items-center justify-center text-sm font-bold">
                  {user.name.split(' ').map(n => n[0]).join('')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold mb-2">Dashboard Overview</h1>
          <p className="text-gray-400">
            Welcome back! Here's what's happening with your translations.
          </p>
        </motion.div>

        {/* Usage Progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-gray-900 rounded-xl border border-gray-800 p-6 mb-8"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Monthly Usage</h2>
            <span className="text-sm text-gray-400">{user.plan} Plan</span>
          </div>
          <div className="mb-2">
            <div className="flex justify-between text-sm mb-1">
              <span>{stats.charactersTranslated.toLocaleString()} characters used</span>
              <span>{stats.planLimit.toLocaleString()} limit</span>
            </div>
            <div className="w-full bg-gray-800 rounded-full h-2">
              <div 
                className="bg-white h-2 rounded-full transition-all duration-500"
                style={{ width: `${stats.monthlyUsage}%` }}
              ></div>
            </div>
          </div>
          <p className="text-xs text-gray-500">
            {stats.monthlyUsage}% of monthly limit used • Resets in 12 days
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <div className="bg-gray-900 p-6 rounded-xl border border-gray-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Documents</p>
                <p className="text-2xl font-bold text-white">{stats.documentsTranslated}</p>
              </div>
              <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center text-xl">
                📄
              </div>
            </div>
          </div>

          <div className="bg-gray-900 p-6 rounded-xl border border-gray-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Characters</p>
                <p className="text-2xl font-bold text-white">{stats.charactersTranslated.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center text-xl">
                📝
              </div>
            </div>
          </div>

          <div className="bg-gray-900 p-6 rounded-xl border border-gray-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Live Sessions</p>
                <p className="text-2xl font-bold text-white">{stats.liveSessionsUsed}</p>
              </div>
              <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center text-xl">
                🎤
              </div>
            </div>
          </div>

          <div className="bg-gray-900 p-6 rounded-xl border border-gray-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Glossaries</p>
                <p className="text-2xl font-bold text-white">{stats.glossariesCreated}</p>
              </div>
              <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center text-xl">
                📚
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          <Link href="/demo" className="bg-white text-black p-6 rounded-xl hover:bg-gray-100 transition-all group">
            <div className="text-2xl mb-2">📄</div>
            <h3 className="text-lg font-semibold mb-2">Translate Text</h3>
            <p className="text-gray-700 text-sm">Quick text translation with our demo tool</p>
          </Link>

          <button className="bg-gray-900 border border-gray-800 p-6 rounded-xl hover:bg-gray-800 transition-all group">
            <div className="text-2xl mb-2">🎤</div>
            <h3 className="text-lg font-semibold mb-2">Start Live Session</h3>
            <p className="text-gray-400 text-sm">Begin real-time translation for meetings</p>
          </button>

          <button className="bg-gray-900 border border-gray-800 p-6 rounded-xl hover:bg-gray-800 transition-all group">
            <div className="text-2xl mb-2">📚</div>
            <h3 className="text-lg font-semibold mb-2">Manage Glossaries</h3>
            <p className="text-gray-400 text-sm">Create custom terminology glossaries</p>
          </button>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-gray-900 rounded-xl border border-gray-800 p-6"
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
            <Link href="/dashboard/translations" className="text-sm text-gray-400 hover:text-white transition-colors">
              View all →
            </Link>
          </div>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg hover:bg-gray-800 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center text-lg">
                    {activity.type === 'document' && '📄'}
                    {activity.type === 'live' && '🎤'}
                    {activity.type === 'glossary' && '📚'}
                  </div>
                  <div>
                    <p className="font-medium text-white">{activity.title}</p>
                    <p className="text-sm text-gray-400">
                      {activity.time} • {activity.size || activity.duration || activity.terms}
                    </p>
                  </div>
                </div>
                <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                  activity.status === 'completed' 
                    ? 'bg-green-900 text-green-300' 
                    : 'bg-yellow-900 text-yellow-300'
                }`}>
                  {activity.status}
                </span>
              </div>
            ))}
          </div>
        </motion.div>
      </main>
    </div>
  );
}
