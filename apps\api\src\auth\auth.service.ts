import { Injectable, UnauthorizedException, ConflictException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from '../prisma/prisma.service';
import { RedisService } from '../redis/redis.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { User } from '@prisma/client';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  async register(registerDto: RegisterDto) {
    const { email, password, name, companyName } = registerDto;

    // Check if user already exists
    const existingUser = await this.prismaService.findUserByEmail(email);
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    try {
      // Create user with optional company
      const userData: any = {
        email,
        password: hashedPassword,
        name,
        preferences: {
          language: 'en',
          theme: 'light',
          notifications: {
            email: true,
            push: true,
          },
        },
        usageQuota: {
          documents: 0,
          liveMinutes: 0,
          charactersTranslated: 0,
        },
      };

      // If company name is provided, create company
      if (companyName) {
        userData.company = {
          create: {
            name: companyName,
            settings: {
              allowSharedGlossaries: true,
              requireApproval: false,
            },
          },
        };
      }

      const user = await this.prismaService.createUser(userData);

      // Generate tokens
      const tokens = await this.generateTokens(user.id);

      // Store refresh token in Redis
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      this.logger.log(`User registered successfully: ${email}`);

      return {
        user: this.sanitizeUser(user),
        ...tokens,
      };
    } catch (error) {
      this.logger.error('Registration failed:', error);
      throw new ConflictException('Registration failed');
    }
  }

  async login(loginDto: LoginDto) {
    const { email, password } = loginDto;

    // Find user by email
    const user = await this.prismaService.findUserByEmail(email);
    if (!user || !user.password) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate tokens
    const tokens = await this.generateTokens(user.id);

    // Store refresh token in Redis
    await this.storeRefreshToken(user.id, tokens.refreshToken);

    this.logger.log(`User logged in successfully: ${email}`);

    return {
      user: this.sanitizeUser(user),
      ...tokens,
    };
  }

  async logout(userId: string, refreshToken?: string) {
    try {
      // Remove refresh token from Redis
      if (refreshToken) {
        await this.removeRefreshToken(userId, refreshToken);
      } else {
        // Remove all refresh tokens for user
        await this.removeAllRefreshTokens(userId);
      }

      this.logger.log(`User logged out successfully: ${userId}`);
    } catch (error) {
      this.logger.error('Logout failed:', error);
    }
  }

  async refreshTokens(refreshToken: string) {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_SECRET'),
      });

      // Check if refresh token exists in Redis
      const storedToken = await this.getRefreshToken(payload.sub);
      if (!storedToken || storedToken !== refreshToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(payload.sub);

      // Update refresh token in Redis
      await this.storeRefreshToken(payload.sub, tokens.refreshToken);

      return tokens;
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.prismaService.findUserByEmail(email);
    if (user && user.password && await bcrypt.compare(password, user.password)) {
      return this.sanitizeUser(user);
    }
    return null;
  }

  async validateGoogleUser(profile: any): Promise<any> {
    const { email, name, picture } = profile;

    let user = await this.prismaService.findUserByEmail(email);

    if (!user) {
      // Create new user from Google profile
      user = await this.prismaService.createUser({
        email,
        name,
        avatar: picture,
        emailVerified: new Date(),
        preferences: {
          language: 'en',
          theme: 'light',
          notifications: {
            email: true,
            push: true,
          },
        },
        usageQuota: {
          documents: 0,
          liveMinutes: 0,
          charactersTranslated: 0,
        },
      });

      this.logger.log(`New user created from Google OAuth: ${email}`);
    }

    return this.sanitizeUser(user);
  }

  async findUserById(id: string) {
    const user = await this.prismaService.findUserById(id);
    return user ? this.sanitizeUser(user) : null;
  }

  private async generateTokens(userId: string) {
    const payload = { sub: userId };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        expiresIn: this.configService.get('JWT_EXPIRES_IN', '15m'),
      }),
      this.jwtService.signAsync(payload, {
        expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d'),
      }),
    ]);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.configService.get('JWT_EXPIRES_IN', '15m'),
    };
  }

  private async storeRefreshToken(userId: string, refreshToken: string) {
    const key = `refresh_token:${userId}`;
    const expiresIn = this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d');
    
    // Convert expires in to seconds
    const expiresInSeconds = this.parseExpiresIn(expiresIn);
    
    await this.redisService.setex(key, expiresInSeconds, refreshToken);
  }

  private async getRefreshToken(userId: string): Promise<string | null> {
    const key = `refresh_token:${userId}`;
    return this.redisService.get(key);
  }

  private async removeRefreshToken(userId: string, refreshToken: string) {
    const key = `refresh_token:${userId}`;
    const storedToken = await this.redisService.get(key);
    
    if (storedToken === refreshToken) {
      await this.redisService.del(key);
    }
  }

  private async removeAllRefreshTokens(userId: string) {
    const key = `refresh_token:${userId}`;
    await this.redisService.del(key);
  }

  private sanitizeUser(user: User & { company?: any }) {
    const { password, ...sanitizedUser } = user;
    return sanitizedUser;
  }

  private parseExpiresIn(expiresIn: string): number {
    const unit = expiresIn.slice(-1);
    const value = parseInt(expiresIn.slice(0, -1));

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'h':
        return value * 60 * 60;
      case 'd':
        return value * 60 * 60 * 24;
      default:
        return 900; // 15 minutes default
    }
  }
}
