@echo off
echo 🚀 Tarjama.ai Quick Start - Fixing Issues...

REM Stop any existing containers
echo [INFO] Stopping any existing containers...
docker-compose down >nul 2>&1

REM Remove old volumes to ensure clean database
echo [INFO] Cleaning up old database volumes...
docker volume rm tarjama_postgres_data >nul 2>&1

REM Install missing dependencies
echo [INFO] Installing missing dependencies...
cd apps\web
call npm install next-themes@^0.2.1
cd ..\..

REM Copy environment file to API directory
if not exist "apps\api\.env" (
    echo [INFO] Copying environment file to API directory...
    copy .env apps\api\.env >nul
)

REM Start Docker services with fresh volumes
echo [INFO] Starting Docker services with fresh database...
docker-compose up -d postgres redis minio

REM Wait longer for PostgreSQL to fully initialize
echo [INFO] Waiting for PostgreSQL to initialize (30 seconds)...
timeout /t 30 /nobreak >nul

REM Check if PostgreSQL is ready
echo [INFO] Checking PostgreSQL connection...
docker-compose exec -T postgres pg_isready -U tarjama_user -d tarjama_dev
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] PostgreSQL not ready yet, waiting another 15 seconds...
    timeout /t 15 /nobreak >nul
)

REM Setup database
echo [INFO] Setting up database schema...
cd apps\api
call npx prisma generate
call npx prisma db push --force-reset
echo [INFO] Seeding database with sample data...
call npx prisma db seed
cd ..\..

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Database setup failed. Let's try a different approach...
    echo [INFO] Trying to create database manually...
    docker-compose exec -T postgres createdb -U tarjama_user tarjama_dev >nul 2>&1
    cd apps\api
    call npx prisma db push --force-reset
    call npx prisma db seed
    cd ..\..
)

REM Start development servers
echo [INFO] Starting development servers...
echo [SUCCESS] Services will be available at:
echo   - Web App: http://localhost:3000
echo   - API: http://localhost:3001  
echo   - API Docs: http://localhost:3001/api/docs
echo   - MinIO Console: http://localhost:9001
echo.
echo [INFO] Press Ctrl+C to stop all services
echo.

REM Start the development servers
start /B npm run dev

echo [INFO] Servers are starting... Check the URLs above in a few seconds.
pause
