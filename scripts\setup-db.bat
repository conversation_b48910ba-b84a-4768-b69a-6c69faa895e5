@echo off
echo 🗄️ Setting up Tarjama.ai Database...

REM Check if PostgreSQL container is running
docker-compose ps postgres | findstr "Up" >nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] PostgreSQL container is not running. Please start it first with:
    echo docker-compose up -d postgres
    pause
    exit /b 1
)

echo [INFO] PostgreSQL container is running. Setting up database...

REM Try to create the database if it doesn't exist
echo [INFO] Ensuring database exists...
docker-compose exec -T postgres psql -U tarjama_user -c "CREATE DATABASE tarjama_dev;" 2>nul

REM Setup database schema using Docker exec to run Prisma from inside a container
echo [INFO] Setting up database schema...
docker-compose exec -T postgres psql -U tarjama_user -d tarjama_dev -c "
CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";
CREATE EXTENSION IF NOT EXISTS \"pg_trgm\";
"

REM Generate Prisma client
echo [INFO] Generating Prisma client...
cd apps\api
call npx prisma generate

REM Push schema to database
echo [INFO] Pushing database schema...
call npx prisma db push --force-reset

REM Create a simple seed script that doesn't require complex operations
echo [INFO] Creating basic admin user...
docker-compose exec -T postgres psql -U tarjama_user -d tarjama_dev -c "
INSERT INTO users (id, email, name, password, role, \"planType\", \"emailVerified\", preferences, \"usageQuota\", \"createdAt\", \"updatedAt\") 
VALUES (
    'admin-user-id', 
    '<EMAIL>', 
    'Admin User', 
    '\$2b\$12\$LQv3c1yqBwlVHpPjrPyMuOehHh9/EfQGdllVR1yAb8sGtqm.Auz9u', 
    'ADMIN', 
    'ENTERPRISE', 
    NOW(), 
    '{\"language\": \"en\", \"theme\": \"light\"}', 
    '{\"documents\": 0, \"liveMinutes\": 0, \"charactersTranslated\": 0}', 
    NOW(), 
    NOW()
) ON CONFLICT (email) DO NOTHING;
"

cd ..\..

echo [SUCCESS] Database setup complete!
echo [INFO] You can now start the development servers with: npm run dev
echo [INFO] Login credentials: <EMAIL> / admin123
pause
