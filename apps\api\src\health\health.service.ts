import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    storage: ServiceHealth;
  };
  metrics: {
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: NodeJS.CpuUsage;
  };
}

export interface ServiceHealth {
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  error?: string;
}

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async getHealthStatus(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString();
    const uptime = Date.now() - this.startTime;
    const version = this.configService.get('npm_package_version', '1.0.0');
    const environment = this.configService.get('NODE_ENV', 'development');

    // Check all services
    const [database, redis, storage] = await Promise.all([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkStorage(),
    ]);

    // Determine overall status
    const services = { database, redis, storage };
    const overallStatus = this.determineOverallStatus(services);

    // Get system metrics
    const metrics = {
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
    };

    return {
      status: overallStatus,
      timestamp,
      uptime,
      version,
      environment,
      services,
      metrics,
    };
  }

  async getReadinessStatus(): Promise<{ ready: boolean; services: any }> {
    const [database, redis] = await Promise.all([
      this.checkDatabase(),
      this.checkRedis(),
    ]);

    const services = { database, redis };
    const ready = database.status === 'healthy' && redis.status === 'healthy';

    return { ready, services };
  }

  async getLivenessStatus(): Promise<{ alive: boolean; uptime: number }> {
    const uptime = Date.now() - this.startTime;
    return { alive: true, uptime };
  }

  private async checkDatabase(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Simple database query to check connectivity
      await this.prisma.$queryRaw`SELECT 1`;
      
      const responseTime = Date.now() - startTime;
      return {
        status: 'healthy',
        responseTime,
      };
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async checkRedis(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Mock Redis check - replace with actual Redis client
      // In a real implementation, you would ping Redis here
      await new Promise(resolve => setTimeout(resolve, 10)); // Simulate Redis ping
      
      const responseTime = Date.now() - startTime;
      return {
        status: 'healthy',
        responseTime,
      };
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async checkStorage(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Mock S3/MinIO check - replace with actual storage client
      // In a real implementation, you would check S3/MinIO connectivity here
      await new Promise(resolve => setTimeout(resolve, 5)); // Simulate storage check
      
      const responseTime = Date.now() - startTime;
      return {
        status: 'healthy',
        responseTime,
      };
    } catch (error) {
      this.logger.error('Storage health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private determineOverallStatus(services: Record<string, ServiceHealth>): 'healthy' | 'unhealthy' | 'degraded' {
    const serviceStatuses = Object.values(services).map(service => service.status);
    
    if (serviceStatuses.every(status => status === 'healthy')) {
      return 'healthy';
    }
    
    if (serviceStatuses.some(status => status === 'unhealthy')) {
      // If database is unhealthy, system is unhealthy
      if (services.database.status === 'unhealthy') {
        return 'unhealthy';
      }
      // If only non-critical services are unhealthy, system is degraded
      return 'degraded';
    }
    
    return 'healthy';
  }

  async getSystemMetrics() {
    const uptime = Date.now() - this.startTime;
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    // Get database statistics
    const dbStats = await this.getDatabaseStats();

    return {
      uptime,
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      database: dbStats,
    };
  }

  private async getDatabaseStats() {
    try {
      const [userCount, documentCount, translationCount, glossaryCount] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.document.count(),
        this.prisma.translation.count(),
        this.prisma.glossary.count(),
      ]);

      return {
        users: userCount,
        documents: documentCount,
        translations: translationCount,
        glossaries: glossaryCount,
      };
    } catch (error) {
      this.logger.error('Error getting database stats:', error);
      return {
        error: 'Unable to retrieve database statistics',
      };
    }
  }
}
