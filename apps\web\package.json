{"name": "@tarjama/web", "version": "1.0.0", "description": "Tarjama.ai Frontend Web Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "clean": "rm -rf .next"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "next-auth": "^4.24.5", "@next-auth/prisma-adapter": "^1.0.7", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "react-dropzone": "^14.2.3", "react-pdf": "^7.5.1", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "date-fns": "^2.30.0", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0", "@radix-ui/react-slot": "^1.0.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/react-window": "^1.8.8", "@types/react-syntax-highlighter": "^15.5.11", "typescript": "^5.3.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "@playwright/test": "^1.40.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "msw": "^2.0.8"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}