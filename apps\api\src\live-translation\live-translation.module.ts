import { Module } from "@nestjs/common";
import { LiveTranslationController } from "./live-translation.controller";
import { LiveTranslationService } from "./live-translation.service";
import { PrismaModule } from "../prisma/prisma.module";

@Module({
  imports: [PrismaModule],
  controllers: [LiveTranslationController],
  providers: [LiveTranslationService],
  exports: [LiveTranslationService],
})
export class LiveTranslationModule {}
