<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Tarjama.ai</title>
    <style>
        body {
            background-color: black;
            color: white;
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .hero {
            text-align: center;
            padding: 60px 0;
        }
        .hero h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .gradient-text {
            background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: white;
            color: black;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.2s;
        }
        .btn:hover {
            background: #f3f4f6;
            transform: scale(1.05);
        }
        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>Professional <span class="gradient-text">Arabic-English Translation</span> for Construction Teams</h1>
            <p>Streamline your construction projects with real-time translation, document processing, and specialized terminology management. Built specifically for the construction industry.</p>
            <a href="#" class="btn">Start Free Trial</a>
            <a href="#" class="btn">Watch Demo</a>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📄 Document Translation</h3>
                <p>Upload PDFs, images, and Word documents for instant AI-powered translation with construction-specific terminology.</p>
            </div>
            <div class="card">
                <h3>🎤 Live Translation</h3>
                <p>Real-time voice and text translation for meetings, site visits, and team collaboration across language barriers.</p>
            </div>
            <div class="card">
                <h3>📚 Smart Glossaries</h3>
                <p>Build and manage custom glossaries with construction terminology for consistent, accurate translations across projects.</p>
            </div>
        </div>
    </div>
</body>
</html>
