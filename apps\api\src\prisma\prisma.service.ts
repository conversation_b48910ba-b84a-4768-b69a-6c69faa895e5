import {
  Injectable,
  On<PERSON><PERSON>ule<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nestjs/common";
import { PrismaClient } from "@prisma/client";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(PrismaService.name);

  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get("DATABASE_URL"),
        },
      },
      log: ["query", "info", "warn", "error"],
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      this.logger.log("Successfully connected to database");
    } catch (error) {
      this.logger.error("Failed to connect to database:", error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.$disconnect();
      this.logger.log("Disconnected from database");
    } catch (error) {
      this.logger.error("Error disconnecting from database:", error);
    }
  }

  async enableShutdownHooks(app: any) {
    // Simplified shutdown hook - removed problematic $on method
  }

  // Helper methods for common operations
  async findUserByEmail(email: string) {
    return this.user.findUnique({
      where: { email },
      include: {
        company: true,
        accounts: true,
      },
    });
  }

  async findUserById(id: string) {
    return this.user.findUnique({
      where: { id },
      include: {
        company: true,
      },
    });
  }

  async createUser(data: any) {
    return this.user.create({
      data,
      include: {
        company: true,
        accounts: true,
      },
    });
  }

  async updateUser(id: string, data: any) {
    return this.user.update({
      where: { id },
      data,
      include: {
        company: true,
      },
    });
  }

  async deleteUser(id: string) {
    return this.user.delete({
      where: { id },
    });
  }

  // Document operations
  async createDocument(data: any) {
    return this.document.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        translation: true,
      },
    });
  }

  async findDocumentById(id: string) {
    return this.document.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        translation: true,
      },
    });
  }

  async findDocumentsByUserId(userId: string, skip = 0, take = 10) {
    return this.document.findMany({
      where: { userId },
      include: {
        translation: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take,
    });
  }

  // Translation operations
  async createTranslation(data: any) {
    return this.translation.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        glossary: true,
      },
    });
  }

  async findTranslationsByUserId(userId: string, skip = 0, take = 10) {
    return this.translation.findMany({
      where: { userId },
      include: {
        glossary: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take,
    });
  }

  // Glossary operations
  async createGlossary(data: any) {
    return this.glossary.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  async findGlossariesByUserId(userId: string) {
    return this.glossary.findMany({
      where: {
        OR: [
          { userId },
          {
            isShared: true,
            user: {
              companyId: {
                not: null,
              },
            },
          },
        ],
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });
  }

  // Analytics operations
  async createOrUpdateUserAnalytics(userId: string, date: Date, data: any) {
    return this.userAnalytics.upsert({
      where: {
        userId_date: {
          userId,
          date,
        },
      },
      update: data,
      create: {
        userId,
        date,
        ...data,
      },
    });
  }

  async getUserAnalytics(userId: string, startDate: Date, endDate: Date) {
    return this.userAnalytics.findMany({
      where: {
        userId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        date: "asc",
      },
    });
  }
}
