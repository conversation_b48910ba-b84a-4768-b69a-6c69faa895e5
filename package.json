{"name": "tarjama-ai", "version": "1.0.0", "description": "AI-powered Arabic-English translation platform for construction professionals", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "test": "turbo run test", "test:e2e": "turbo run test:e2e", "type-check": "turbo run type-check", "clean": "turbo run clean", "db:generate": "cd apps/api && npm run db:generate", "db:push": "cd apps/api && npm run db:push", "db:migrate": "cd apps/api && npm run db:migrate", "db:studio": "cd apps/api && npm run db:studio", "db:seed": "cd apps/api && npm run db:seed", "db:setup": "npm run db:generate && npm run db:push && npm run db:seed", "db:reset": "cd apps/api && npm run db:push -- --force-reset && npm run db:seed", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "services:start": "docker-compose up postgres redis minio -d", "services:stop": "docker-compose stop postgres redis minio", "dev:all": "npm run services:start && sleep 10 && turbo run dev", "dev:web": "cd apps/web && npm run dev", "dev:api": "cd apps/api && npm run start:dev", "setup": "node scripts/check-setup.js && npm install", "health": "curl -f http://localhost:3001/health || echo 'API not ready' && curl -f http://localhost:3000 || echo 'Web not ready'", "k8s:apply": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete -f k8s/", "check": "node scripts/check-setup.js"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "repository": {"type": "git", "url": "https://github.com/tarjama-ai/tarjama-platform.git"}, "keywords": ["translation", "arabic", "construction", "ai", "saas", "nextjs", "<PERSON><PERSON><PERSON>"], "author": "Tarjama.ai Team", "license": "UNLICENSED"}