import {
  Injectable,
  On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { createClient, RedisClientType } from "redis";

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private client: RedisClientType;
  private readonly logger = new Logger(RedisService.name);

  constructor(private configService: ConfigService) {
    const redisUrl = this.configService.get<string>("REDIS_URL");

    this.client = createClient({
      url: redisUrl,
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            this.logger.error("Redis connection failed after 10 retries");
            return new Error("Redis connection failed");
          }
          return Math.min(retries * 50, 1000);
        },
      },
    });

    this.client.on("error", (error) => {
      this.logger.error("Redis client error:", error);
    });

    this.client.on("connect", () => {
      this.logger.log("Redis client connected");
    });

    this.client.on("ready", () => {
      this.logger.log("Redis client ready");
    });

    this.client.on("end", () => {
      this.logger.log("Redis client disconnected");
    });
  }

  async onModuleInit() {
    try {
      await this.client.connect();
      this.logger.log("Successfully connected to Redis");
    } catch (error) {
      this.logger.error("Failed to connect to Redis:", error);
      this.logger.warn(
        "Continuing without Redis - some features may be limited"
      );
      // Don't throw error in development to allow testing without Redis
    }
  }

  async onModuleDestroy() {
    try {
      await this.client.quit();
      this.logger.log("Disconnected from Redis");
    } catch (error) {
      this.logger.error("Error disconnecting from Redis:", error);
    }
  }

  // Basic Redis operations
  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key);
    } catch (error) {
      this.logger.error(`Error getting key ${key}:`, error);
      return null;
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<boolean> {
    try {
      if (ttl) {
        await this.client.setEx(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
      return true;
    } catch (error) {
      this.logger.error(`Error setting key ${key}:`, error);
      return false;
    }
  }

  async setex(key: string, seconds: number, value: string): Promise<boolean> {
    try {
      await this.client.setEx(key, seconds, value);
      return true;
    } catch (error) {
      this.logger.error(`Error setting key ${key} with expiry:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting key ${key}:`, error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`Error checking existence of key ${key}:`, error);
      return false;
    }
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, seconds);
      return Number(result) === 1;
    } catch (error) {
      this.logger.error(`Error setting expiry for key ${key}:`, error);
      return false;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      this.logger.error(`Error getting TTL for key ${key}:`, error);
      return -1;
    }
  }

  async ping(): Promise<string> {
    try {
      return await this.client.ping();
    } catch (error) {
      this.logger.error("Error pinging Redis:", error);
      throw error;
    }
  }

  // Hash operations
  async hget(key: string, field: string): Promise<string | null> {
    try {
      return await this.client.hGet(key, field);
    } catch (error) {
      this.logger.error(
        `Error getting hash field ${field} from ${key}:`,
        error
      );
      return null;
    }
  }

  async hset(key: string, field: string, value: string): Promise<boolean> {
    try {
      await this.client.hSet(key, field, value);
      return true;
    } catch (error) {
      this.logger.error(`Error setting hash field ${field} in ${key}:`, error);
      return false;
    }
  }

  async hgetall(key: string): Promise<Record<string, string> | null> {
    try {
      return await this.client.hGetAll(key);
    } catch (error) {
      this.logger.error(`Error getting all hash fields from ${key}:`, error);
      return null;
    }
  }

  async hdel(key: string, field: string): Promise<boolean> {
    try {
      const result = await this.client.hDel(key, field);
      return result === 1;
    } catch (error) {
      this.logger.error(
        `Error deleting hash field ${field} from ${key}:`,
        error
      );
      return false;
    }
  }

  // List operations
  async lpush(key: string, value: string): Promise<number> {
    try {
      return await this.client.lPush(key, value);
    } catch (error) {
      this.logger.error(`Error pushing to list ${key}:`, error);
      return 0;
    }
  }

  async rpush(key: string, value: string): Promise<number> {
    try {
      return await this.client.rPush(key, value);
    } catch (error) {
      this.logger.error(`Error pushing to list ${key}:`, error);
      return 0;
    }
  }

  async lpop(key: string): Promise<string | null> {
    try {
      return await this.client.lPop(key);
    } catch (error) {
      this.logger.error(`Error popping from list ${key}:`, error);
      return null;
    }
  }

  async rpop(key: string): Promise<string | null> {
    try {
      return await this.client.rPop(key);
    } catch (error) {
      this.logger.error(`Error popping from list ${key}:`, error);
      return null;
    }
  }

  async llen(key: string): Promise<number> {
    try {
      return await this.client.lLen(key);
    } catch (error) {
      this.logger.error(`Error getting list length ${key}:`, error);
      return 0;
    }
  }

  // Set operations
  async sadd(key: string, member: string): Promise<boolean> {
    try {
      const result = await this.client.sAdd(key, member);
      return result === 1;
    } catch (error) {
      this.logger.error(`Error adding to set ${key}:`, error);
      return false;
    }
  }

  async srem(key: string, member: string): Promise<boolean> {
    try {
      const result = await this.client.sRem(key, member);
      return result === 1;
    } catch (error) {
      this.logger.error(`Error removing from set ${key}:`, error);
      return false;
    }
  }

  async smembers(key: string): Promise<string[]> {
    try {
      return await this.client.sMembers(key);
    } catch (error) {
      this.logger.error(`Error getting set members ${key}:`, error);
      return [];
    }
  }

  async sismember(key: string, member: string): Promise<boolean> {
    try {
      const result = await this.client.sIsMember(key, member);
      return Number(result) === 1;
    } catch (error) {
      this.logger.error(`Error checking set membership ${key}:`, error);
      return false;
    }
  }

  // JSON operations (if Redis Stack is available)
  async jsonGet(key: string, path = "$"): Promise<any> {
    try {
      // @ts-ignore - Redis JSON commands might not be typed
      return await this.client.json.get(key, { path });
    } catch (error) {
      this.logger.error(`Error getting JSON from ${key}:`, error);
      return null;
    }
  }

  async jsonSet(key: string, path: string, value: any): Promise<boolean> {
    try {
      // @ts-ignore - Redis JSON commands might not be typed
      await this.client.json.set(key, path, value);
      return true;
    } catch (error) {
      this.logger.error(`Error setting JSON in ${key}:`, error);
      return false;
    }
  }

  // Cache helper methods
  async cacheGet<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error(`Error getting cached value ${key}:`, error);
      return null;
    }
  }

  async cacheSet<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      return await this.set(key, serialized, ttl);
    } catch (error) {
      this.logger.error(`Error caching value ${key}:`, error);
      return false;
    }
  }

  // Rate limiting helper
  async incrementCounter(key: string, ttl: number): Promise<number> {
    try {
      const multi = this.client.multi();
      multi.incr(key);
      multi.expire(key, ttl);
      const results = await multi.exec();
      return results[0] as number;
    } catch (error) {
      this.logger.error(`Error incrementing counter ${key}:`, error);
      return 0;
    }
  }

  // Session management
  async setSession(
    sessionId: string,
    data: any,
    ttl: number
  ): Promise<boolean> {
    const key = `session:${sessionId}`;
    return await this.cacheSet(key, data, ttl);
  }

  async getSession(sessionId: string): Promise<any> {
    const key = `session:${sessionId}`;
    return await this.cacheGet(key);
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    const key = `session:${sessionId}`;
    return await this.del(key);
  }

  // Get Redis client for advanced operations
  getClient(): RedisClientType {
    return this.client;
  }
}
