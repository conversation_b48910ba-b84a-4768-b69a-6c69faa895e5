import {
  Controller,
  Get,
  Put,
  Delete,
  Param,
  Query,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { NotificationService, NotificationPreferences } from './notification.service';
import { IsBoolean, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class NotificationTypesDto {
  @IsOptional()
  @IsBoolean()
  SYSTEM?: boolean;

  @IsOptional()
  @IsBoolean()
  TRANSLATION_COMPLETE?: boolean;

  @IsOptional()
  @IsBoolean()
  QUOTA_WARNING?: boolean;

  @IsOptional()
  @IsBoolean()
  QUOTA_EXCEEDED?: boolean;

  @IsOptional()
  @IsBoolean()
  SUBSCRIPTION_EXPIRING?: boolean;

  @IsOptional()
  @IsBoolean()
  FEATURE_ANNOUNCEMENT?: boolean;
}

export class UpdateNotificationPreferencesDto {
  @IsOptional()
  @IsBoolean()
  email?: boolean;

  @IsOptional()
  @IsBoolean()
  push?: boolean;

  @IsOptional()
  @IsBoolean()
  inApp?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => NotificationTypesDto)
  types?: NotificationTypesDto;
}

@ApiTags('notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get user notifications',
    description: 'Retrieve paginated list of user notifications with optional filtering'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Notifications retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        notifications: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              type: { type: 'string' },
              title: { type: 'string' },
              message: { type: 'string' },
              data: { type: 'object' },
              isRead: { type: 'boolean' },
              readAt: { type: 'string', format: 'date-time' },
              createdAt: { type: 'string', format: 'date-time' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
        unreadCount: { type: 'number' },
      },
    },
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ name: 'unreadOnly', required: false, type: Boolean, description: 'Show only unread notifications' })
  async getNotifications(
    @Request() req: any,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('unreadOnly') unreadOnly: string = 'false',
  ) {
    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 20));
    const unreadOnlyBool = unreadOnly.toLowerCase() === 'true';
    
    return await this.notificationService.getUserNotifications(
      req.user.id,
      pageNum,
      limitNum,
      unreadOnlyBool,
    );
  }

  @Put(':id/read')
  @ApiOperation({ 
    summary: 'Mark notification as read',
    description: 'Mark a specific notification as read'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Notification marked as read successfully',
  })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  async markAsRead(
    @Request() req: any,
    @Param('id') notificationId: string,
  ) {
    return await this.notificationService.markAsRead(req.user.id, notificationId);
  }

  @Put('read-all')
  @ApiOperation({ 
    summary: 'Mark all notifications as read',
    description: 'Mark all user notifications as read'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'All notifications marked as read successfully',
  })
  async markAllAsRead(@Request() req: any) {
    return await this.notificationService.markAllAsRead(req.user.id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete notification',
    description: 'Delete a specific notification'
  })
  @ApiResponse({ status: 204, description: 'Notification deleted successfully' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  async deleteNotification(
    @Request() req: any,
    @Param('id') notificationId: string,
  ) {
    await this.notificationService.deleteNotification(req.user.id, notificationId);
  }

  @Get('preferences')
  @ApiOperation({ 
    summary: 'Get notification preferences',
    description: 'Get user notification preferences and settings'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Notification preferences retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        email: { type: 'boolean' },
        push: { type: 'boolean' },
        inApp: { type: 'boolean' },
        types: {
          type: 'object',
          properties: {
            SYSTEM: { type: 'boolean' },
            TRANSLATION_COMPLETE: { type: 'boolean' },
            QUOTA_WARNING: { type: 'boolean' },
            QUOTA_EXCEEDED: { type: 'boolean' },
            SUBSCRIPTION_EXPIRING: { type: 'boolean' },
            FEATURE_ANNOUNCEMENT: { type: 'boolean' },
          },
        },
      },
    },
  })
  async getPreferences(@Request() req: any) {
    return await this.notificationService.getNotificationPreferences(req.user.id);
  }

  @Put('preferences')
  @ApiOperation({ 
    summary: 'Update notification preferences',
    description: 'Update user notification preferences and settings'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Notification preferences updated successfully',
  })
  @ApiBody({ type: UpdateNotificationPreferencesDto })
  async updatePreferences(
    @Request() req: any,
    @Body(ValidationPipe) preferences: UpdateNotificationPreferencesDto,
  ) {
    return await this.notificationService.updateNotificationPreferences(
      req.user.id,
      preferences,
    );
  }
}
