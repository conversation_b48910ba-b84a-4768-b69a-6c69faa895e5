# Database Configuration
DATABASE_URL="postgresql://tarjama_user:tarjama_password@localhost:5432/tarjama_dev"
REDIS_URL="redis://:redis_password@localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-super-secret-nextauth-key-change-in-production"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# OpenAI Configuration
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL="gpt-4o"
OPENAI_MAX_TOKENS=4000

# Google Cloud Configuration (for Vision API)
GOOGLE_CLOUD_PROJECT_ID="your-google-cloud-project-id"
GOOGLE_CLOUD_KEY_FILE="path/to/your/service-account-key.json"

# DeepL Configuration
DEEPL_API_KEY="your-deepl-api-key"
DEEPL_API_URL="https://api-free.deepl.com/v2"

# AWS Configuration
AWS_ACCESS_KEY_ID="your-aws-access-key-id"
AWS_SECRET_ACCESS_KEY="your-aws-secret-access-key"
AWS_REGION="me-south-1"
S3_BUCKET_NAME="tarjama-uploads"
S3_ENDPOINT="https://s3.me-south-1.amazonaws.com"

# MinIO Configuration (for local development)
MINIO_ENDPOINT="http://localhost:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin123"
MINIO_BUCKET_NAME="tarjama-dev-uploads"

# Application Configuration
NODE_ENV="development"
PORT=3000
API_PORT=3001

# Frontend Configuration
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_WS_URL="ws://localhost:3001"
NEXT_PUBLIC_APP_NAME="Tarjama.ai"
NEXT_PUBLIC_APP_DESCRIPTION="AI-powered Arabic-English translation for construction professionals"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# Monitoring Configuration
SENTRY_DSN="your-sentry-dsn"
SENTRY_ENVIRONMENT="development"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES="pdf,jpg,jpeg,png,doc,docx"

# Translation Configuration
MAX_TRANSLATION_LENGTH=10000
TRANSLATION_TIMEOUT=30000

# WebSocket Configuration
WS_CORS_ORIGIN="http://localhost:3000"
WS_MAX_CONNECTIONS=1000

# Logging Configuration
LOG_LEVEL="debug"
LOG_FORMAT="combined"

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-in-production"
CORS_ORIGIN="http://localhost:3000"

# Feature Flags
ENABLE_LIVE_TRANSLATION=true
ENABLE_GLOSSARY_SHARING=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# Subscription Configuration
STRIPE_PUBLIC_KEY="your-stripe-public-key"
STRIPE_SECRET_KEY="your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="your-stripe-webhook-secret"

# Analytics Configuration
GOOGLE_ANALYTICS_ID="your-google-analytics-id"
MIXPANEL_TOKEN="your-mixpanel-token"

# Support Configuration
INTERCOM_APP_ID="your-intercom-app-id"
ZENDESK_SUBDOMAIN="your-zendesk-subdomain"
ZENDESK_API_TOKEN="your-zendesk-api-token"
