"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslate } from "@/hooks/useApi";
import { LoadingButton, LoadingState } from "@/components/ui/loading";
import { motion } from "framer-motion";
import { api } from "@/lib/api";

export default function DemoPage() {
  const [sourceText, setSourceText] = useState(
    "مشروع البناء يتطلب مواد عالية الجودة"
  );
  const [sourceLanguage, setSourceLanguage] = useState("ar");
  const [targetLanguage, setTargetLanguage] = useState("en");
  const [apiStatus, setApiStatus] = useState<
    "checking" | "connected" | "disconnected"
  >("checking");
  const [translationCount, setTranslationCount] = useState(0);

  const {
    data: translationResult,
    loading: isTranslating,
    error,
    translate,
  } = useTranslate();

  // Rate limiting for free users
  const MAX_FREE_TRANSLATIONS = 5;
  const MAX_FREE_CHARACTERS = 500;

  // Check API connection on component mount
  useEffect(() => {
    const checkApiConnection = async () => {
      try {
        await api.healthCheck();
        setApiStatus("connected");
      } catch (error) {
        console.error("API connection failed:", error);
        setApiStatus("disconnected");
      }
    };

    checkApiConnection();
  }, []);

  const handleTranslate = async () => {
    if (!sourceText.trim()) return;

    setError(null);

    // Check rate limits for free users
    if (translationCount >= MAX_FREE_TRANSLATIONS) {
      setError(
        `Free demo limit reached (${MAX_FREE_TRANSLATIONS} translations). Please sign up for unlimited access.`
      );
      return;
    }

    if (sourceText.length > MAX_FREE_CHARACTERS) {
      setError(
        `Text too long (${sourceText.length} characters). Free demo limit is ${MAX_FREE_CHARACTERS} characters.`
      );
      return;
    }

    setIsTranslating(true);

    try {
      let translationResult = null;

      if (apiStatus === "connected") {
        try {
          // Try real API translation
          const result = await api.translate({
            text: sourceText,
            sourceLanguage,
            targetLanguage,
            context: "construction",
          });
          translationResult = result.translatedText;
          setTranslationCount((prev) => prev + 1);
        } catch (apiError) {
          console.warn(
            "API translation failed, falling back to mock:",
            apiError
          );
          // Fall through to mock translation
        }
      }

      if (!translationResult) {
        // Fallback to mock translation
        await new Promise((resolve) => setTimeout(resolve, 1500));
        const mockTranslations = {
          "ar-en": {
            "مشروع البناء يتطلب مواد عالية الجودة":
              "The construction project requires high-quality materials",
            "يجب فحص الأساسات قبل البدء في البناء":
              "The foundations must be inspected before starting construction",
            "المقاول مسؤول عن سلامة الموقع":
              "The contractor is responsible for site safety",
            "معدات السلامة إلزامية في الموقع":
              "Safety equipment is mandatory on site",
            "يرجى مراجعة الرسومات المعمارية":
              "Please review the architectural drawings",
            "تم الانتهاء من أعمال الحفر": "Excavation work has been completed",
            "نحتاج إلى موافقة المهندس": "We need the engineer's approval",
            "الخرسانة جاهزة للصب": "The concrete is ready for pouring",
            "فحص جودة المواد": "Quality inspection of materials",
            "جدولة أعمال البناء": "Construction work scheduling",
          },
          "en-ar": {
            "The construction project requires high-quality materials":
              "مشروع البناء يتطلب مواد عالية الجودة",
            "Safety equipment is mandatory on site":
              "معدات السلامة إلزامية في الموقع",
            "Please review the architectural drawings":
              "يرجى مراجعة الرسومات المعمارية",
            "Excavation work has been completed": "تم الانتهاء من أعمال الحفر",
            "We need the engineer's approval": "نحتاج إلى موافقة المهندس",
            "The concrete is ready for pouring": "الخرسانة جاهزة للصب",
            "Quality inspection of materials": "فحص جودة المواد",
            "Construction work scheduling": "جدولة أعمال البناء",
            "Building permit required": "مطلوب رخصة بناء",
            "Site preparation is complete": "تم الانتهاء من تحضير الموقع",
          },
        };

        const key = `${sourceLanguage}-${targetLanguage}`;
        const translation =
          mockTranslations[key]?.[sourceText] ||
          (sourceLanguage === "ar"
            ? "Construction project translation (demo)"
            : "ترجمة مشروع البناء (تجريبي)");

        translationResult = translation;
        setTranslationCount((prev) => prev + 1);
      }

      setTranslatedText(translationResult);
    } catch (error) {
      console.error("Translation failed:", error);
      setError("Translation failed. Please try again.");
    } finally {
      setIsTranslating(false);
    }
  };

  const swapLanguages = () => {
    setSourceLanguage(targetLanguage);
    setTargetLanguage(sourceLanguage);
    setSourceText(translatedText);
    setTranslatedText(sourceText);
  };

  const sampleTexts = {
    ar: [
      "مشروع البناء يتطلب مواد عالية الجودة",
      "يجب فحص الأساسات قبل البدء في البناء",
      "المقاول مسؤول عن سلامة الموقع",
    ],
    en: [
      "The construction project requires high-quality materials",
      "Safety equipment is mandatory on site",
      "Please review the architectural drawings",
    ],
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white">
      {/* Header */}
      <header className="border-b border-slate-800 bg-slate-950/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="text-2xl font-bold gradient-text">
              Tarjama.ai
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/login" className="btn-ghost">
                Login
              </Link>
              <Link href="/register" className="btn-primary">
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4">
            Try <span className="gradient-text">Tarjama.ai</span> Demo
          </h1>
          <p className="text-xl text-slate-400 max-w-2xl mx-auto mb-4">
            Experience our AI-powered Arabic-English translation designed
            specifically for construction professionals.
          </p>

          {/* API Status Indicator */}
          <div className="flex flex-col items-center space-y-2">
            <div className="flex items-center space-x-2">
              <div
                className={`w-2 h-2 rounded-full ${
                  apiStatus === "connected"
                    ? "bg-green-500"
                    : apiStatus === "disconnected"
                      ? "bg-red-500"
                      : "bg-yellow-500 animate-pulse"
                }`}
              ></div>
              <span className="text-sm text-slate-500">
                {apiStatus === "connected"
                  ? "API Connected"
                  : apiStatus === "disconnected"
                    ? "API Disconnected (Demo Mode)"
                    : "Checking API..."}
              </span>
            </div>

            {/* Usage Counter */}
            <div className="text-xs text-slate-600">
              Free demo: {translationCount}/{MAX_FREE_TRANSLATIONS} translations
              used
            </div>
          </div>
        </motion.div>

        {/* Translation Interface */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-slate-800/50 rounded-xl border border-slate-700 p-6 mb-8"
        >
          {/* Language Selector */}
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-4">
              <select
                value={sourceLanguage}
                onChange={(e) => setSourceLanguage(e.target.value)}
                className="bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="ar">Arabic</option>
                <option value="en">English</option>
              </select>

              <button
                onClick={swapLanguages}
                className="p-2 hover:bg-slate-700 rounded-lg transition-colors"
                title="Swap languages"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                  />
                </svg>
              </button>

              <select
                value={targetLanguage}
                onChange={(e) => setTargetLanguage(e.target.value)}
                className="bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="en">English</option>
                <option value="ar">Arabic</option>
              </select>
            </div>
          </div>

          {/* Translation Areas */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* Source Text */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-slate-300">
                  Source Text ({sourceLanguage === "ar" ? "Arabic" : "English"})
                </label>
                <div className="text-xs text-slate-500">
                  <span
                    className={
                      sourceText.length > MAX_FREE_CHARACTERS
                        ? "text-red-400 font-medium"
                        : "text-slate-400"
                    }
                  >
                    {sourceText.length}
                  </span>
                  <span className="text-slate-600">
                    /{MAX_FREE_CHARACTERS} characters
                  </span>
                </div>
              </div>
              <div className="relative">
                <textarea
                  value={sourceText}
                  onChange={(e) => setSourceText(e.target.value)}
                  className={`w-full h-32 p-4 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                    sourceLanguage === "ar" ? "text-right" : "text-left"
                  } ${sourceText.length > MAX_FREE_CHARACTERS ? "border-red-500 focus:ring-red-500" : ""}`}
                  placeholder={
                    sourceLanguage === "ar"
                      ? "أدخل النص العربي هنا..."
                      : "Enter English text here..."
                  }
                  dir={sourceLanguage === "ar" ? "rtl" : "ltr"}
                />
                {/* Character limit warning */}
                {sourceText.length > MAX_FREE_CHARACTERS && (
                  <div className="absolute bottom-2 right-2 bg-red-900/80 text-red-300 text-xs px-2 py-1 rounded">
                    Exceeds free limit
                  </div>
                )}
              </div>
            </div>

            {/* Translated Text */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Translation ({targetLanguage === "ar" ? "Arabic" : "English"})
              </label>
              <div
                className={`w-full h-32 p-4 bg-slate-900 border border-slate-600 rounded-lg text-slate-300 ${
                  targetLanguage === "ar" ? "text-right" : "text-left"
                }`}
                dir={targetLanguage === "ar" ? "rtl" : "ltr"}
              >
                {isTranslating ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                      <span>Translating...</span>
                    </div>
                  </div>
                ) : (
                  translatedText || (
                    <span className="text-slate-500">
                      {targetLanguage === "ar"
                        ? "الترجمة ستظهر هنا..."
                        : "Translation will appear here..."}
                    </span>
                  )
                )}
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mt-4 p-4 bg-red-900/20 border border-red-800 rounded-lg">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Translate Button */}
          <div className="flex justify-center mt-6">
            <button
              onClick={handleTranslate}
              disabled={
                isTranslating ||
                !sourceText.trim() ||
                translationCount >= MAX_FREE_TRANSLATIONS
              }
              className="btn-primary px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isTranslating
                ? "Translating..."
                : translationCount >= MAX_FREE_TRANSLATIONS
                  ? "Demo Limit Reached"
                  : "Translate"}
            </button>
          </div>
        </motion.div>

        {/* Sample Texts */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-8"
        >
          <h3 className="text-lg font-semibold mb-4">
            Try these construction examples:
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">
                Arabic Examples
              </h4>
              <div className="space-y-2">
                {sampleTexts.ar.map((text, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSourceLanguage("ar");
                      setTargetLanguage("en");
                      setSourceText(text);
                      setTranslatedText("");
                    }}
                    className="w-full p-3 bg-slate-800 hover:bg-slate-700 rounded-lg text-right text-sm transition-colors"
                    dir="rtl"
                  >
                    {text}
                  </button>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">
                English Examples
              </h4>
              <div className="space-y-2">
                {sampleTexts.en.map((text, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSourceLanguage("en");
                      setTargetLanguage("ar");
                      setSourceText(text);
                      setTranslatedText("");
                    }}
                    className="w-full p-3 bg-slate-800 hover:bg-slate-700 rounded-lg text-left text-sm transition-colors"
                  >
                    {text}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="text-center bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl p-8 border border-slate-700"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to get started?</h3>
          <p className="text-slate-300 mb-6">
            Join thousands of construction professionals using Tarjama.ai for
            their translation needs.
          </p>
          <Link href="/register" className="btn-primary text-lg px-8 py-3">
            Start Your Free Trial
          </Link>
        </motion.div>
      </main>
    </div>
  );
}
