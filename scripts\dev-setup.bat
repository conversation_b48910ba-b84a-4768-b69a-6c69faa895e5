@echo off
REM Tarjama.ai Development Environment Setup Script for Windows
REM This script sets up the development environment and starts all services

echo 🚀 Setting up Tarjama.ai Development Environment...

REM Check if required tools are installed
echo [INFO] Checking requirements...

where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 20+ and try again.
    pause
    exit /b 1
)

where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] npm is not installed. Please install npm and try again.
    pause
    exit /b 1
)

where docker >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop and try again.
    pause
    exit /b 1
)

where docker-compose >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose and try again.
    pause
    exit /b 1
)

echo [SUCCESS] All requirements are met!

REM Check if .env file exists
echo [INFO] Checking environment configuration...

if not exist ".env" (
    echo [WARNING] .env file not found. Creating from .env.example...
    copy .env.example .env
    echo [SUCCESS] Created .env file. Please review and update the values as needed.
) else (
    echo [SUCCESS] .env file found!
)

REM Install dependencies
echo [INFO] Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed!

REM Start Docker services
echo [INFO] Starting Docker services (PostgreSQL, Redis, MinIO)...
call docker-compose down
call docker-compose up -d postgres redis minio

REM Wait for services to be ready
echo [INFO] Waiting for services to be ready...
timeout /t 15 /nobreak >nul

REM Setup database
echo [INFO] Setting up database...
cd apps\api
call npx prisma generate
call npx prisma db push
call npx prisma db seed
cd ..\..
echo [SUCCESS] Database setup complete!

REM Setup MinIO bucket
echo [INFO] Setting up MinIO bucket...
timeout /t 5 /nobreak >nul
docker-compose exec -T minio mc alias set local http://localhost:9000 minioadmin minioadmin123 2>nul
docker-compose exec -T minio mc mb local/tarjama-dev-uploads 2>nul
echo [SUCCESS] MinIO bucket created!

REM Start development servers
echo [INFO] Starting development servers...
echo [SUCCESS] Development servers starting...
echo [INFO] API will be available at: http://localhost:3001
echo [INFO] Web app will be available at: http://localhost:3000
echo [INFO] API documentation will be available at: http://localhost:3001/api/docs
echo [INFO] MinIO console will be available at: http://localhost:9001
echo [WARNING] Press Ctrl+C to stop all services

call npm run dev

REM Cleanup
echo [INFO] Stopping services...
call docker-compose down
echo [SUCCESS] All services stopped!

pause
