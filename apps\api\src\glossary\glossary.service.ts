import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Glossary } from '@prisma/client';

export interface CreateGlossaryDto {
  name: string;
  description?: string;
  sourceLanguage: string;
  targetLanguage: string;
  isShared?: boolean;
  terms?: GlossaryTerm[];
}

export interface UpdateGlossaryDto {
  name?: string;
  description?: string;
  isShared?: boolean;
  terms?: GlossaryTerm[];
}

export interface GlossaryTerm {
  source: string;
  target: string;
  context?: string;
  category?: string;
}

export interface ImportGlossaryDto {
  name: string;
  description?: string;
  sourceLanguage: string;
  targetLanguage: string;
  terms: GlossaryTerm[];
}

@Injectable()
export class GlossaryService {
  private readonly logger = new Logger(GlossaryService.name);

  constructor(private readonly prisma: PrismaService) {}

  async createGlossary(userId: string, createDto: CreateGlossaryDto): Promise<Glossary> {
    try {
      // Validate languages
      if (!['ar', 'en'].includes(createDto.sourceLanguage) || 
          !['ar', 'en'].includes(createDto.targetLanguage)) {
        throw new BadRequestException('Only Arabic (ar) and English (en) languages are supported');
      }

      if (createDto.sourceLanguage === createDto.targetLanguage) {
        throw new BadRequestException('Source and target languages must be different');
      }

      // Validate terms
      if (createDto.terms) {
        this.validateTerms(createDto.terms);
      }

      const glossary = await this.prisma.glossary.create({
        data: {
          userId,
          name: createDto.name,
          description: createDto.description,
          sourceLanguage: createDto.sourceLanguage,
          targetLanguage: createDto.targetLanguage,
          isShared: createDto.isShared || false,
          terms: createDto.terms || [],
          termCount: createDto.terms?.length || 0,
        },
      });

      this.logger.log(`Glossary created: ${glossary.id} for user ${userId}`);
      return glossary;
    } catch (error) {
      this.logger.error(`Error creating glossary for user ${userId}:`, error);
      throw error;
    }
  }

  async getGlossaries(
    userId: string,
    page: number = 1,
    limit: number = 20,
    includeShared: boolean = true,
  ) {
    try {
      const skip = (page - 1) * limit;
      const where: any = {
        OR: [
          { userId },
        ],
      };

      if (includeShared) {
        where.OR.push({ isShared: true });
      }

      const [glossaries, total] = await Promise.all([
        this.prisma.glossary.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                translations: true,
              },
            },
          },
        }),
        this.prisma.glossary.count({ where }),
      ]);

      return {
        glossaries,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Error getting glossaries for user ${userId}:`, error);
      throw error;
    }
  }

  async getGlossaryById(userId: string, glossaryId: string): Promise<Glossary> {
    try {
      const glossary = await this.prisma.glossary.findFirst({
        where: {
          id: glossaryId,
          OR: [
            { userId },
            { isShared: true },
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              translations: true,
            },
          },
        },
      });

      if (!glossary) {
        throw new NotFoundException('Glossary not found');
      }

      return glossary;
    } catch (error) {
      this.logger.error(`Error getting glossary ${glossaryId} for user ${userId}:`, error);
      throw error;
    }
  }

  async updateGlossary(
    userId: string,
    glossaryId: string,
    updateDto: UpdateGlossaryDto,
  ): Promise<Glossary> {
    try {
      // Check if user owns the glossary
      const existingGlossary = await this.prisma.glossary.findFirst({
        where: {
          id: glossaryId,
          userId,
        },
      });

      if (!existingGlossary) {
        throw new ForbiddenException('You can only update your own glossaries');
      }

      // Validate terms if provided
      if (updateDto.terms) {
        this.validateTerms(updateDto.terms);
      }

      const updatedGlossary = await this.prisma.glossary.update({
        where: { id: glossaryId },
        data: {
          ...updateDto,
          termCount: updateDto.terms?.length ?? existingGlossary.termCount,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Glossary updated: ${glossaryId} by user ${userId}`);
      return updatedGlossary;
    } catch (error) {
      this.logger.error(`Error updating glossary ${glossaryId} for user ${userId}:`, error);
      throw error;
    }
  }

  async deleteGlossary(userId: string, glossaryId: string): Promise<void> {
    try {
      // Check if user owns the glossary
      const existingGlossary = await this.prisma.glossary.findFirst({
        where: {
          id: glossaryId,
          userId,
        },
      });

      if (!existingGlossary) {
        throw new ForbiddenException('You can only delete your own glossaries');
      }

      await this.prisma.glossary.delete({
        where: { id: glossaryId },
      });

      this.logger.log(`Glossary deleted: ${glossaryId} by user ${userId}`);
    } catch (error) {
      this.logger.error(`Error deleting glossary ${glossaryId} for user ${userId}:`, error);
      throw error;
    }
  }

  async addTerms(
    userId: string,
    glossaryId: string,
    newTerms: GlossaryTerm[],
  ): Promise<Glossary> {
    try {
      const glossary = await this.getGlossaryById(userId, glossaryId);

      if (glossary.userId !== userId) {
        throw new ForbiddenException('You can only modify your own glossaries');
      }

      this.validateTerms(newTerms);

      const existingTerms = Array.isArray(glossary.terms) ? glossary.terms : [];
      const updatedTerms = [...existingTerms, ...newTerms];

      const updatedGlossary = await this.prisma.glossary.update({
        where: { id: glossaryId },
        data: {
          terms: updatedTerms,
          termCount: updatedTerms.length,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Terms added to glossary: ${glossaryId} by user ${userId}`);
      return updatedGlossary;
    } catch (error) {
      this.logger.error(`Error adding terms to glossary ${glossaryId} for user ${userId}:`, error);
      throw error;
    }
  }

  async importGlossary(userId: string, importDto: ImportGlossaryDto): Promise<Glossary> {
    try {
      this.validateTerms(importDto.terms);

      const glossary = await this.createGlossary(userId, {
        name: importDto.name,
        description: importDto.description,
        sourceLanguage: importDto.sourceLanguage,
        targetLanguage: importDto.targetLanguage,
        terms: importDto.terms,
      });

      this.logger.log(`Glossary imported: ${glossary.id} for user ${userId} with ${importDto.terms.length} terms`);
      return glossary;
    } catch (error) {
      this.logger.error(`Error importing glossary for user ${userId}:`, error);
      throw error;
    }
  }

  async searchTerms(
    userId: string,
    query: string,
    sourceLanguage?: string,
    targetLanguage?: string,
  ) {
    try {
      const where: any = {
        OR: [
          { userId },
          { isShared: true },
        ],
      };

      if (sourceLanguage) {
        where.sourceLanguage = sourceLanguage;
      }

      if (targetLanguage) {
        where.targetLanguage = targetLanguage;
      }

      const glossaries = await this.prisma.glossary.findMany({
        where,
        select: {
          id: true,
          name: true,
          terms: true,
          sourceLanguage: true,
          targetLanguage: true,
        },
      });

      const matchingTerms: any[] = [];

      for (const glossary of glossaries) {
        const terms = Array.isArray(glossary.terms) ? glossary.terms : [];
        
        for (const term of terms) {
          if (
            term.source?.toLowerCase().includes(query.toLowerCase()) ||
            term.target?.toLowerCase().includes(query.toLowerCase())
          ) {
            matchingTerms.push({
              ...term,
              glossaryId: glossary.id,
              glossaryName: glossary.name,
              sourceLanguage: glossary.sourceLanguage,
              targetLanguage: glossary.targetLanguage,
            });
          }
        }
      }

      return {
        query,
        results: matchingTerms,
        count: matchingTerms.length,
      };
    } catch (error) {
      this.logger.error(`Error searching terms for user ${userId}:`, error);
      throw error;
    }
  }

  private validateTerms(terms: GlossaryTerm[]): void {
    if (!Array.isArray(terms)) {
      throw new BadRequestException('Terms must be an array');
    }

    for (const term of terms) {
      if (!term.source || !term.target) {
        throw new BadRequestException('Each term must have both source and target text');
      }

      if (term.source.length > 500 || term.target.length > 500) {
        throw new BadRequestException('Term text cannot exceed 500 characters');
      }
    }

    if (terms.length > 10000) {
      throw new BadRequestException('Cannot have more than 10,000 terms in a glossary');
    }
  }
}
