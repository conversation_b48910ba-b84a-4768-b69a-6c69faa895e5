import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { Cron, CronExpression } from "@nestjs/schedule";
import { Prisma } from "@prisma/client";

export interface AnalyticsOverview {
  totalUsers: number;
  totalDocuments: number;
  totalTranslations: number;
  totalGlossaries: number;
  totalLiveSessions: number;
  charactersTranslated: number;
  averageConfidence: number;
  topLanguagePairs: Array<{
    sourceLanguage: string;
    targetLanguage: string;
    count: number;
  }>;
}

export interface UserAnalytics {
  userId: string;
  period: string;
  documentsProcessed: number;
  charactersTranslated: number;
  liveMinutesUsed: number;
  glossaryTermsUsed: number;
  averageConfidence: number;
  userSatisfaction: number;
}

export interface UsageMetrics {
  daily: Array<{
    date: string;
    users: number;
    documents: number;
    translations: number;
    characters: number;
  }>;
  weekly: Array<{
    week: string;
    users: number;
    documents: number;
    translations: number;
    characters: number;
  }>;
  monthly: Array<{
    month: string;
    users: number;
    documents: number;
    translations: number;
    characters: number;
  }>;
}

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(private readonly prisma: PrismaService) {}

  async getOverview(userId?: string): Promise<AnalyticsOverview> {
    try {
      const where = userId ? { userId } : {};

      const [
        totalUsers,
        totalDocuments,
        totalTranslations,
        totalGlossaries,
        totalLiveSessions,
        translationStats,
        languagePairs,
      ] = await Promise.all([
        userId ? 1 : this.prisma.user.count(),
        this.prisma.document.count({ where }),
        this.prisma.translation.count({ where }),
        this.prisma.glossary.count({ where }),
        this.prisma.liveSession.count({ where }),
        this.prisma.translation.aggregate({
          where,
          _sum: { characterCount: true },
          _avg: { confidenceScore: true },
        }),
        this.getTopLanguagePairs(userId),
      ]);

      return {
        totalUsers,
        totalDocuments,
        totalTranslations,
        totalGlossaries,
        totalLiveSessions,
        charactersTranslated: translationStats._sum.characterCount || 0,
        averageConfidence: translationStats._avg.confidenceScore || 0,
        topLanguagePairs: languagePairs,
      };
    } catch (error) {
      this.logger.error("Error getting analytics overview:", error);
      throw error;
    }
  }

  async getUserAnalytics(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<UserAnalytics[]> {
    try {
      const analytics = await this.prisma.userAnalytics.findMany({
        where: {
          userId,
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: { date: "asc" },
      });

      return analytics.map((record) => ({
        userId: record.userId,
        period: record.date.toISOString().split("T")[0],
        documentsProcessed: record.documentsProcessed,
        charactersTranslated: record.charactersTranslated,
        liveMinutesUsed: record.liveMinutesUsed,
        glossaryTermsUsed: record.glossaryTermsUsed,
        averageConfidence: record.averageConfidence || 0,
        userSatisfaction: record.userSatisfaction || 0,
      }));
    } catch (error) {
      this.logger.error(`Error getting user analytics for ${userId}:`, error);
      throw error;
    }
  }

  async getUsageMetrics(
    userId?: string,
    days: number = 30
  ): Promise<UsageMetrics> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const where = userId ? { userId } : {};

      // Get daily metrics
      const dailyMetrics = await this.getDailyMetrics(
        startDate,
        endDate,
        where
      );
      const weeklyMetrics = await this.getWeeklyMetrics(
        startDate,
        endDate,
        where
      );
      const monthlyMetrics = await this.getMonthlyMetrics(
        startDate,
        endDate,
        where
      );

      return {
        daily: dailyMetrics,
        weekly: weeklyMetrics,
        monthly: monthlyMetrics,
      };
    } catch (error) {
      this.logger.error("Error getting usage metrics:", error);
      throw error;
    }
  }

  async trackUserActivity(
    userId: string,
    activity: {
      documentsProcessed?: number;
      charactersTranslated?: number;
      liveMinutesUsed?: number;
      glossaryTermsUsed?: number;
    }
  ): Promise<void> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      await this.prisma.userAnalytics.upsert({
        where: {
          userId_date: {
            userId,
            date: today,
          },
        },
        update: {
          documentsProcessed: {
            increment: activity.documentsProcessed || 0,
          },
          charactersTranslated: {
            increment: activity.charactersTranslated || 0,
          },
          liveMinutesUsed: {
            increment: activity.liveMinutesUsed || 0,
          },
          glossaryTermsUsed: {
            increment: activity.glossaryTermsUsed || 0,
          },
        },
        create: {
          userId,
          date: today,
          documentsProcessed: activity.documentsProcessed || 0,
          charactersTranslated: activity.charactersTranslated || 0,
          liveMinutesUsed: activity.liveMinutesUsed || 0,
          glossaryTermsUsed: activity.glossaryTermsUsed || 0,
        },
      });
    } catch (error) {
      this.logger.error(`Error tracking user activity for ${userId}:`, error);
      // Don't throw error as this is not critical
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async generateDailyReports(): Promise<void> {
    try {
      this.logger.log("Generating daily analytics reports...");

      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);

      const endOfYesterday = new Date(yesterday);
      endOfYesterday.setHours(23, 59, 59, 999);

      // Get all users who had activity yesterday
      const activeUsers = await this.prisma.user.findMany({
        where: {
          OR: [
            {
              documents: {
                some: {
                  createdAt: {
                    gte: yesterday,
                    lte: endOfYesterday,
                  },
                },
              },
            },
            {
              translations: {
                some: {
                  createdAt: {
                    gte: yesterday,
                    lte: endOfYesterday,
                  },
                },
              },
            },
          ],
        },
        select: { id: true },
      });

      // Generate analytics for each active user
      for (const user of activeUsers) {
        await this.generateUserDailyAnalytics(user.id, yesterday);
      }

      this.logger.log(
        `Generated daily reports for ${activeUsers.length} users`
      );
    } catch (error) {
      this.logger.error("Error generating daily reports:", error);
    }
  }

  private async generateUserDailyAnalytics(
    userId: string,
    date: Date
  ): Promise<void> {
    try {
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const [documentsCount, translationsData, liveSessionsData] =
        await Promise.all([
          this.prisma.document.count({
            where: {
              userId,
              createdAt: {
                gte: date,
                lte: endOfDay,
              },
            },
          }),
          this.prisma.translation.aggregate({
            where: {
              userId,
              createdAt: {
                gte: date,
                lte: endOfDay,
              },
            },
            _count: true,
            _sum: { characterCount: true },
            _avg: { confidenceScore: true },
          }),
          this.prisma.liveSession.aggregate({
            where: {
              userId,
              createdAt: {
                gte: date,
                lte: endOfDay,
              },
            },
            _sum: { durationSeconds: true },
          }),
        ]);

      await this.prisma.userAnalytics.upsert({
        where: {
          userId_date: {
            userId,
            date,
          },
        },
        update: {
          documentsProcessed: documentsCount,
          charactersTranslated: translationsData._sum.characterCount || 0,
          liveMinutesUsed: Math.round(
            (liveSessionsData._sum.durationSeconds || 0) / 60
          ),
          averageConfidence: translationsData._avg.confidenceScore || null,
        },
        create: {
          userId,
          date,
          documentsProcessed: documentsCount,
          charactersTranslated: translationsData._sum.characterCount || 0,
          liveMinutesUsed: Math.round(
            (liveSessionsData._sum.durationSeconds || 0) / 60
          ),
          averageConfidence: translationsData._avg.confidenceScore || null,
        },
      });
    } catch (error) {
      this.logger.error(
        `Error generating daily analytics for user ${userId}:`,
        error
      );
    }
  }

  private async getTopLanguagePairs(userId?: string) {
    try {
      const where = userId ? { userId } : {};

      // Use raw query to avoid complex TypeScript issues with groupBy
      const languagePairs = (await this.prisma.$queryRaw`
        SELECT
          "sourceLanguage",
          "targetLanguage",
          COUNT(*) as count
        FROM "translations"
        ${userId ? Prisma.sql`WHERE "userId" = ${userId}` : Prisma.empty}
        GROUP BY "sourceLanguage", "targetLanguage"
        ORDER BY count DESC
        LIMIT 5
      `) as Array<{
        sourceLanguage: string;
        targetLanguage: string;
        count: bigint;
      }>;

      return languagePairs.map((pair) => ({
        sourceLanguage: pair.sourceLanguage,
        targetLanguage: pair.targetLanguage,
        count: Number(pair.count),
      }));
    } catch (error) {
      this.logger.error("Error getting top language pairs:", error);
      return [];
    }
  }

  private async getDailyMetrics(startDate: Date, endDate: Date, where: any) {
    // Mock implementation - in real app, you'd query the database
    const days = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const metrics = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);

      metrics.push({
        date: date.toISOString().split("T")[0],
        users: Math.floor(Math.random() * 100),
        documents: Math.floor(Math.random() * 50),
        translations: Math.floor(Math.random() * 200),
        characters: Math.floor(Math.random() * 10000),
      });
    }

    return metrics;
  }

  private async getWeeklyMetrics(startDate: Date, endDate: Date, where: any) {
    // Mock implementation
    return [
      {
        week: "2024-01",
        users: 150,
        documents: 75,
        translations: 300,
        characters: 15000,
      },
    ];
  }

  private async getMonthlyMetrics(startDate: Date, endDate: Date, where: any) {
    // Mock implementation
    return [
      {
        month: "2024-01",
        users: 500,
        documents: 250,
        translations: 1000,
        characters: 50000,
      },
    ];
  }
}
