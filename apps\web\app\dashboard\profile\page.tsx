"use client";

import { useState } from "react";

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [profile, setProfile] = useState({
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+966 50 123 4567",
    company: "Dubai Construction Co.",
    position: "Project Manager",
    location: "Dubai, UAE",
    bio: "Experienced project manager specializing in large-scale construction projects across the Middle East. Passionate about leveraging technology to improve communication and efficiency in construction teams.",
    avatar: null as File | null,
  });

  const [stats] = useState({
    totalTranslations: 1247,
    totalCharacters: 89432,
    totalSessions: 23,
    joinDate: "2023-08-15",
    lastActive: "2024-01-15T14:30:00Z",
    favoriteLanguagePair: "Arabic → English",
    accuracy: 99.2,
  });

  const handleSave = () => {
    // Save profile logic here
    setIsEditing(false);
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setProfile(prev => ({ ...prev, avatar: file }));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">Profile</h1>
          <p className="text-gray-400 mt-2">Manage your personal information and preferences</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
              {/* Avatar */}
              <div className="relative mb-6">
                <div className="w-24 h-24 bg-white text-black rounded-full flex items-center justify-center text-2xl font-bold mx-auto">
                  {profile.firstName[0]}{profile.lastName[0]}
                </div>
                {isEditing && (
                  <label className="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-2 bg-white text-black p-2 rounded-full cursor-pointer hover:bg-gray-100 transition-colors">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarChange}
                      className="hidden"
                    />
                  </label>
                )}
              </div>

              <h2 className="text-xl font-bold text-white mb-2">
                {profile.firstName} {profile.lastName}
              </h2>
              <p className="text-gray-400 mb-1">{profile.position}</p>
              <p className="text-gray-400 mb-4">{profile.company}</p>
              <p className="text-sm text-gray-500 mb-6">{profile.location}</p>

              {/* Quick Stats */}
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Member since</span>
                  <span className="text-white">{formatDate(stats.joinDate)}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Translations</span>
                  <span className="text-white">{stats.totalTranslations.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Accuracy</span>
                  <span className="text-white">{stats.accuracy}%</span>
                </div>
              </div>
            </div>

            {/* Achievement Badges */}
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 mt-6">
              <h3 className="text-lg font-semibold text-white mb-4">Achievements</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 bg-gray-800/50 rounded-lg">
                  <div className="text-2xl mb-1">🏆</div>
                  <div className="text-xs text-gray-400">Power User</div>
                </div>
                <div className="text-center p-3 bg-gray-800/50 rounded-lg">
                  <div className="text-2xl mb-1">🎯</div>
                  <div className="text-xs text-gray-400">Accuracy Pro</div>
                </div>
                <div className="text-center p-3 bg-gray-800/50 rounded-lg">
                  <div className="text-2xl mb-1">📚</div>
                  <div className="text-xs text-gray-400">Glossary Master</div>
                </div>
                <div className="text-center p-3 bg-gray-800/50 rounded-lg">
                  <div className="text-2xl mb-1">🌟</div>
                  <div className="text-xs text-gray-400">Early Adopter</div>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Details */}
          <div className="lg:col-span-2">
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">Personal Information</h3>
                <button
                  onClick={() => isEditing ? handleSave() : setIsEditing(true)}
                  className="bg-white text-black px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
                >
                  {isEditing ? "Save Changes" : "Edit Profile"}
                </button>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      First Name
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profile.firstName}
                        onChange={(e) => setProfile(prev => ({ ...prev, firstName: e.target.value }))}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      />
                    ) : (
                      <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3">{profile.firstName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Last Name
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profile.lastName}
                        onChange={(e) => setProfile(prev => ({ ...prev, lastName: e.target.value }))}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      />
                    ) : (
                      <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3">{profile.lastName}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                  </label>
                  {isEditing ? (
                    <input
                      type="email"
                      value={profile.email}
                      onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                    />
                  ) : (
                    <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3">{profile.email}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </label>
                  {isEditing ? (
                    <input
                      type="tel"
                      value={profile.phone}
                      onChange={(e) => setProfile(prev => ({ ...prev, phone: e.target.value }))}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                    />
                  ) : (
                    <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3">{profile.phone}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Company
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profile.company}
                        onChange={(e) => setProfile(prev => ({ ...prev, company: e.target.value }))}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      />
                    ) : (
                      <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3">{profile.company}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Position
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profile.position}
                        onChange={(e) => setProfile(prev => ({ ...prev, position: e.target.value }))}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      />
                    ) : (
                      <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3">{profile.position}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Location
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={profile.location}
                      onChange={(e) => setProfile(prev => ({ ...prev, location: e.target.value }))}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                    />
                  ) : (
                    <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3">{profile.location}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Bio
                  </label>
                  {isEditing ? (
                    <textarea
                      value={profile.bio}
                      onChange={(e) => setProfile(prev => ({ ...prev, bio: e.target.value }))}
                      rows={4}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20 resize-none"
                    />
                  ) : (
                    <p className="text-white bg-gray-800/50 rounded-lg px-4 py-3 leading-relaxed">{profile.bio}</p>
                  )}
                </div>
              </div>

              {isEditing && (
                <div className="mt-6 pt-6 border-t border-gray-700 flex items-center justify-between">
                  <button
                    onClick={() => setIsEditing(false)}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
                  >
                    Save Changes
                  </button>
                </div>
              )}
            </div>

            {/* Usage Statistics */}
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-8 mt-6">
              <h3 className="text-xl font-semibold text-white mb-6">Usage Statistics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">{stats.totalTranslations.toLocaleString()}</div>
                  <div className="text-gray-400">Total Translations</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">{stats.totalCharacters.toLocaleString()}</div>
                  <div className="text-gray-400">Characters Translated</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">{stats.totalSessions}</div>
                  <div className="text-gray-400">Live Sessions</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
