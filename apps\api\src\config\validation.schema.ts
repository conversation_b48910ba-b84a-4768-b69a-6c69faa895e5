import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  // Environment
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  
  // Server
  PORT: Joi.number().default(3000),
  API_PORT: Joi.number().default(3001),
  
  // Database
  DATABASE_URL: Joi.string().required(),
  
  // Redis
  REDIS_URL: Joi.string().required(),
  
  // JWT
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),
  
  // NextAuth
  NEXTAUTH_URL: Joi.string().uri().required(),
  NEXTAUTH_SECRET: Joi.string().required(),
  
  // Google OAuth
  GOOGLE_CLIENT_ID: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  GOOGLE_CLIENT_SECRET: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  
  // OpenAI
  OPENAI_API_KEY: Joi.string().required(),
  OPENAI_MODEL: Joi.string().default('gpt-4o'),
  OPENAI_MAX_TOKENS: Joi.number().default(4000),
  
  // Google Cloud
  GOOGLE_CLOUD_PROJECT_ID: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  GOOGLE_CLOUD_KEY_FILE: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  
  // DeepL
  DEEPL_API_KEY: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  DEEPL_API_URL: Joi.string().uri().default('https://api-free.deepl.com/v2'),
  
  // AWS
  AWS_ACCESS_KEY_ID: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  AWS_SECRET_ACCESS_KEY: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  AWS_REGION: Joi.string().default('me-south-1'),
  S3_BUCKET_NAME: Joi.string().required(),
  S3_ENDPOINT: Joi.string().uri().optional(),
  
  // MinIO (for development)
  MINIO_ENDPOINT: Joi.string().uri().when('NODE_ENV', {
    is: 'development',
    then: Joi.default('http://localhost:9000'),
    otherwise: Joi.optional(),
  }),
  MINIO_ACCESS_KEY: Joi.string().when('NODE_ENV', {
    is: 'development',
    then: Joi.default('minioadmin'),
    otherwise: Joi.optional(),
  }),
  MINIO_SECRET_KEY: Joi.string().when('NODE_ENV', {
    is: 'development',
    then: Joi.default('minioadmin123'),
    otherwise: Joi.optional(),
  }),
  MINIO_BUCKET_NAME: Joi.string().when('NODE_ENV', {
    is: 'development',
    then: Joi.default('tarjama-dev-uploads'),
    otherwise: Joi.optional(),
  }),
  
  // Email
  SMTP_HOST: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  SMTP_PORT: Joi.number().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  SMTP_USER: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  SMTP_PASS: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  FROM_EMAIL: Joi.string().email().default('<EMAIL>'),
  
  // Monitoring
  SENTRY_DSN: Joi.string().uri().optional(),
  SENTRY_ENVIRONMENT: Joi.string().default('development'),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: Joi.number().default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  
  // File Upload
  MAX_FILE_SIZE: Joi.number().default(10485760), // 10MB
  ALLOWED_FILE_TYPES: Joi.string().default('pdf,jpg,jpeg,png,doc,docx'),
  
  // Translation
  MAX_TRANSLATION_LENGTH: Joi.number().default(10000),
  TRANSLATION_TIMEOUT: Joi.number().default(30000), // 30 seconds
  
  // WebSocket
  WS_CORS_ORIGIN: Joi.string().default('http://localhost:3000'),
  WS_MAX_CONNECTIONS: Joi.number().default(1000),
  
  // Logging
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug', 'verbose')
    .default('info'),
  LOG_FORMAT: Joi.string()
    .valid('json', 'simple', 'combined')
    .default('combined'),
  
  // Security
  BCRYPT_ROUNDS: Joi.number().default(12),
  SESSION_SECRET: Joi.string().required(),
  CORS_ORIGIN: Joi.string().default('http://localhost:3000'),
  
  // Feature Flags
  ENABLE_LIVE_TRANSLATION: Joi.boolean().default(true),
  ENABLE_GLOSSARY_SHARING: Joi.boolean().default(true),
  ENABLE_ANALYTICS: Joi.boolean().default(true),
  ENABLE_NOTIFICATIONS: Joi.boolean().default(true),
  
  // Subscription (Stripe)
  STRIPE_PUBLIC_KEY: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  STRIPE_SECRET_KEY: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  STRIPE_WEBHOOK_SECRET: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  
  // Analytics
  GOOGLE_ANALYTICS_ID: Joi.string().optional(),
  MIXPANEL_TOKEN: Joi.string().optional(),
  
  // Support
  INTERCOM_APP_ID: Joi.string().optional(),
  ZENDESK_SUBDOMAIN: Joi.string().optional(),
  ZENDESK_API_TOKEN: Joi.string().optional(),
});
