"use client";

import { useState } from "react";
import { motion } from "framer-motion";

export default function DashboardPage() {
  const [user] = useState({
    name: "<PERSON>",
    company: "Dubai Construction Co.",
    plan: "Professional",
  });

  const [stats] = useState({
    documentsTranslated: 156,
    charactersTranslated: 45230,
    liveSessionsUsed: 23,
    glossariesCreated: 8,
  });

  const [recentActivity] = useState([
    {
      id: 1,
      type: "document",
      title: "Construction Contract - Phase 2",
      status: "completed",
      time: "2 hours ago",
    },
    {
      id: 2,
      type: "live",
      title: "Site Meeting Translation",
      status: "completed",
      time: "5 hours ago",
    },
    {
      id: 3,
      type: "glossary",
      title: "Updated HVAC Terminology",
      status: "completed",
      time: "1 day ago",
    },
  ]);

  return (
    <div className="min-h-screen bg-slate-950 text-white">
      {/* Header */}
      <header className="border-b border-slate-800 bg-slate-950/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold gradient-text">
              Tarjama.ai
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-slate-300">Welcome back, {user.name}</span>
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-sm font-bold">
                {user.name.split(' ').map(n => n[0]).join('')}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
          <p className="text-slate-400">
            Manage your translations and track your progress
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <div className="bg-slate-800/50 p-6 rounded-xl border border-slate-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Documents Translated</p>
                <p className="text-2xl font-bold text-white">{stats.documentsTranslated}</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                📄
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 p-6 rounded-xl border border-slate-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Characters Translated</p>
                <p className="text-2xl font-bold text-white">{stats.charactersTranslated.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                📝
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 p-6 rounded-xl border border-slate-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Live Sessions</p>
                <p className="text-2xl font-bold text-white">{stats.liveSessionsUsed}</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                🎤
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 p-6 rounded-xl border border-slate-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Glossaries Created</p>
                <p className="text-2xl font-bold text-white">{stats.glossariesCreated}</p>
              </div>
              <div className="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
                📚
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          <button className="bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-xl text-left hover:from-blue-600 hover:to-blue-700 transition-all">
            <div className="text-2xl mb-2">📄</div>
            <h3 className="text-lg font-semibold mb-2">Translate Document</h3>
            <p className="text-blue-100 text-sm">Upload and translate PDFs, images, or Word documents</p>
          </button>

          <button className="bg-gradient-to-br from-purple-500 to-purple-600 p-6 rounded-xl text-left hover:from-purple-600 hover:to-purple-700 transition-all">
            <div className="text-2xl mb-2">🎤</div>
            <h3 className="text-lg font-semibold mb-2">Start Live Session</h3>
            <p className="text-purple-100 text-sm">Begin real-time translation for meetings or calls</p>
          </button>

          <button className="bg-gradient-to-br from-green-500 to-green-600 p-6 rounded-xl text-left hover:from-green-600 hover:to-green-700 transition-all">
            <div className="text-2xl mb-2">📚</div>
            <h3 className="text-lg font-semibold mb-2">Manage Glossaries</h3>
            <p className="text-green-100 text-sm">Create and edit custom terminology glossaries</p>
          </button>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-slate-800/50 rounded-xl border border-slate-700 p-6"
        >
          <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-slate-600 rounded-lg flex items-center justify-center">
                    {activity.type === 'document' && '📄'}
                    {activity.type === 'live' && '🎤'}
                    {activity.type === 'glossary' && '📚'}
                  </div>
                  <div>
                    <p className="font-medium text-white">{activity.title}</p>
                    <p className="text-sm text-slate-400">{activity.time}</p>
                  </div>
                </div>
                <span className="px-3 py-1 bg-green-500/20 text-green-400 text-xs font-medium rounded-full">
                  {activity.status}
                </span>
              </div>
            ))}
          </div>
        </motion.div>
      </main>
    </div>
  );
}
