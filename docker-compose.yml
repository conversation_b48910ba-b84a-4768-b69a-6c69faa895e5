services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: tarjama-postgres
    environment:
      POSTGRES_DB: tarjama_dev
      POSTGRES_USER: tarjama_user
      POSTGRES_PASSWORD: tarjama_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./apps/api/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - tarjama-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tarjama_user -d tarjama_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tarjama-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tarjama-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --requirepass redis_password

  # MinIO (S3-compatible storage for development)
  minio:
    image: minio/minio:latest
    container_name: tarjama-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - tarjama-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # API Backend (NestJS)
  api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile
      target: development
    container_name: tarjama-api
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************************/tarjama_dev
      REDIS_URL: redis://:redis_password@redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GOOGLE_CLOUD_PROJECT_ID: ${GOOGLE_CLOUD_PROJECT_ID}
      GOOGLE_CLOUD_KEY_FILE: ${GOOGLE_CLOUD_KEY_FILE}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: us-east-1
      S3_BUCKET_NAME: tarjama-dev-uploads
      S3_ENDPOINT: http://minio:9000
      DEEPL_API_KEY: ${DEEPL_API_KEY}
    ports:
      - "3001:3000"
    volumes:
      - ./apps/api:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    networks:
      - tarjama-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    restart: unless-stopped

  # Frontend Web App (Next.js)
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
      target: development
    container_name: tarjama-web
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001
      NEXT_PUBLIC_WS_URL: ws://localhost:3001
      NEXTAUTH_URL: http://localhost:3000
      NEXTAUTH_SECRET: your-super-secret-nextauth-key-change-in-production
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
    ports:
      - "3000:3000"
    volumes:
      - ./apps/web:/app
      - /app/node_modules
      - /app/.next
    networks:
      - tarjama-network
    depends_on:
      - api
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: tarjama-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - tarjama-network
    depends_on:
      - web
      - api
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  tarjama-network:
    driver: bridge
