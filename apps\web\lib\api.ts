const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export interface TranslationRequest {
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
}

export interface TranslationResponse {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence?: number;
  provider?: string;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api/v1${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request('/health');
  }

  // Translation
  async translate(data: TranslationRequest): Promise<TranslationResponse> {
    return this.request('/translations/translate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Get translation history
  async getTranslationHistory(page = 1, limit = 20): Promise<{
    translations: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    return this.request(`/translations/history?page=${page}&limit=${limit}`);
  }

  // Auth endpoints
  async register(data: {
    email: string;
    password: string;
    name: string;
  }): Promise<{ user: any; tokens: any }> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async login(data: {
    email: string;
    password: string;
  }): Promise<{ user: any; tokens: any }> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Glossary endpoints
  async getGlossaries(): Promise<any[]> {
    return this.request('/glossaries');
  }

  async createGlossary(data: {
    name: string;
    description?: string;
    sourceLanguage: string;
    targetLanguage: string;
    terms?: Array<{
      source: string;
      target: string;
      context?: string;
      category?: string;
    }>;
  }): Promise<any> {
    return this.request('/glossaries', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

// Create a singleton instance
export const apiClient = new ApiClient();

// Export individual functions for convenience
export const api = {
  healthCheck: () => apiClient.healthCheck(),
  translate: (data: TranslationRequest) => apiClient.translate(data),
  getTranslationHistory: (page?: number, limit?: number) => 
    apiClient.getTranslationHistory(page, limit),
  register: (data: { email: string; password: string; name: string }) => 
    apiClient.register(data),
  login: (data: { email: string; password: string }) => 
    apiClient.login(data),
  getGlossaries: () => apiClient.getGlossaries(),
  createGlossary: (data: any) => apiClient.createGlossary(data),
};

export default apiClient;
