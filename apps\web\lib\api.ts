const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";

export interface TranslationRequest {
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
}

export interface TranslationResponse {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence?: number;
  provider?: string;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
    this.token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api/v1${endpoint}`;

    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      headers,
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        if (response.status === 401) {
          this.clearToken();
          // Redirect to login or emit auth error event
          if (typeof window !== "undefined") {
            window.location.href = "/login";
          }
        }

        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request("/health");
  }

  // Translation
  async translate(data: TranslationRequest): Promise<TranslationResponse> {
    return this.request("/translations/translate", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Get translation history
  async getTranslationHistory(
    page = 1,
    limit = 20
  ): Promise<{
    translations: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    return this.request(`/translations/history?page=${page}&limit=${limit}`);
  }

  // Auth endpoints
  async register(data: {
    email: string;
    password: string;
    name: string;
  }): Promise<{ user: any; tokens: any }> {
    return this.request("/auth/register", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async login(data: {
    email: string;
    password: string;
  }): Promise<{ user: any; tokens: any }> {
    return this.request("/auth/login", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Glossary endpoints
  async getGlossaries(): Promise<any[]> {
    return this.request("/glossaries");
  }

  async createGlossary(data: {
    name: string;
    description?: string;
    sourceLanguage: string;
    targetLanguage: string;
    terms?: Array<{
      source: string;
      target: string;
      context?: string;
      category?: string;
    }>;
  }): Promise<any> {
    return this.request("/glossaries", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Documents
  async uploadDocument(
    file: File,
    options: {
      sourceLanguage: string;
      targetLanguage: string;
      ocrEnabled?: boolean;
    }
  ) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("sourceLanguage", options.sourceLanguage);
    formData.append("targetLanguage", options.targetLanguage);
    if (options.ocrEnabled !== undefined) {
      formData.append("ocrEnabled", options.ocrEnabled.toString());
    }

    return this.request<{
      documentId: string;
      status: string;
      estimatedTime?: number;
    }>("/documents/upload", {
      method: "POST",
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it with boundary
    });
  }

  async getDocuments(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    return this.request<{
      documents: any[];
      total: number;
      page: number;
      totalPages: number;
    }>(`/documents?${queryParams}`);
  }

  async downloadDocument(id: string) {
    const url = `${this.baseUrl}/api/v1/documents/${id}/download`;
    const headers: HeadersInit = {};

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return fetch(url, { headers });
  }

  // Analytics
  async getAnalytics(params?: { timeRange?: string; metrics?: string[] }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach((v) => queryParams.append(key, v));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }

    return this.request<any>(`/analytics?${queryParams}`);
  }

  // User Profile
  async getProfile() {
    return this.request<any>("/user/profile");
  }

  async updateProfile(data: any) {
    return this.request<any>("/user/profile", {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async updateSettings(data: any) {
    return this.request<any>("/user/settings", {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }
}

// Create a singleton instance
export const apiClient = new ApiClient();

// Export individual functions for convenience
export const api = {
  healthCheck: () => apiClient.healthCheck(),
  translate: (data: TranslationRequest) => apiClient.translate(data),
  getTranslationHistory: (page?: number, limit?: number) =>
    apiClient.getTranslationHistory(page, limit),
  register: (data: { email: string; password: string; name: string }) =>
    apiClient.register(data),
  login: (data: { email: string; password: string }) => apiClient.login(data),
  getGlossaries: () => apiClient.getGlossaries(),
  createGlossary: (data: any) => apiClient.createGlossary(data),
};

export default apiClient;
