"use client";

import { motion } from "framer-motion";

const testimonials = [
  {
    body: "Tarjama.ai has revolutionized how we handle multilingual construction projects. The accuracy and speed are incredible, especially for technical specifications.",
    author: {
      name: "<PERSON>",
      handle: "Project Manager",
      company: "Dubai Construction Co.",
      avatar: "AR",
    },
  },
  {
    body: "Finally, a translation tool that understands construction terminology. It saves us hours every day and eliminates costly miscommunications.",
    author: {
      name: "<PERSON>",
      handle: "Site Engineer",
      company: "Global Infrastructure Ltd.",
      avatar: "SM",
    },
  },
  {
    body: "The live translation feature during meetings is a game-changer. Our international teams communicate seamlessly now, improving project efficiency by 40%.",
    author: {
      name: "<PERSON>",
      handle: "Construction Director",
      company: "NEOM Project",
      avatar: "OH",
    },
  },
];

export function TestimonialsSection() {
  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Trusted by construction professionals
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            See what industry leaders are saying about Tarjama.ai
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="flex flex-col bg-background p-6 rounded-lg shadow-sm"
            >
              <div className="flex gap-x-1 text-primary">
                {[...Array(5)].map((_, i) => (
                  <StarIcon key={i} className="h-5 w-5" />
                ))}
              </div>
              <blockquote className="mt-6 text-lg leading-8">
                <p>"{testimonial.body}"</p>
              </blockquote>
              <figcaption className="mt-6 flex items-center gap-x-4">
                <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                  {testimonial.author.name.charAt(0)}
                </div>
                <div>
                  <div className="font-semibold">{testimonial.author.name}</div>
                  <div className="text-muted-foreground">
                    {testimonial.author.handle} at {testimonial.author.company}
                  </div>
                </div>
              </figcaption>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
