PS C:\Users\<USER>\OneDrive\Desktop\Tarjama> docker-compose down
[+] Running 4/4
 ✔ Container tarjama-postgres       Removed                                                                                                                       0.9s 
 ✔ Container tarjama-minio          Removed                                                                                                                       0.7s 
 ✔ Container tarjama-redis          Removed                                                                                                                       1.2s 
 ✔ Network tarjama_tarjama-network  Removed                                                                                                                       0.6s 
PS C:\Users\<USER>\OneDrive\Desktop\Tarjama> scripts\quick-start.bat
≡ƒÜÇ Tarjama.ai Quick Start - Fixing Issues...
[INFO] Stopping any existing containers...
[INFO] Cleaning up old database volumes...
[INFO] Installing missing dependencies...
npm warn deprecated npmlog@5.0.1: This package is no longer supported.
npm warn deprecated are-we-there-yet@2.0.0: This package is no longer supported.
npm warn deprecated gauge@3.0.2: This package is no longer supported.

added 1 package, and audited 1571 packages in 5s

359 packages are looking for funding
  run `npm fund` for details

5 vulnerabilities (3 moderate, 2 high)

To address all issues (including breaking changes), run:
  npm audit fix --force

Run `npm audit` for details.
[INFO] Starting Docker services with fresh database...
[+] Running 5/5
 ✔ Network tarjama_tarjama-network  Created                                                                                                                       0.0s 
 ✔ Volume "tarjama_postgres_data"   Created                                                                                                                       0.0s 
 ✔ Container tarjama-minio          Started                                                                                                                       0.5s 
 ✔ Container tarjama-redis          Started                                                                                                                       0.5s 
 ✔ Container tarjama-postgres       Started                                                                                                                       0.5s 
[INFO] Waiting for PostgreSQL to initialize (30 seconds)...
[INFO] Checking PostgreSQL connection...
/var/run/postgresql:5432 - accepting connections
[INFO] Setting up database schema...
Environment variables loaded from .env
Prisma schema loaded from prisma\schema.prisma

✔ Generated Prisma Client (v5.22.0) to .\..\..\node_modules\@prisma\client in 79ms

Start by importing your Prisma Client (See: https://pris.ly/d/importing-client)

Help us improve the Prisma ORM for everyone. Share your feedback in a short 2-min survey: https://pris.ly/orm/survey/release-5-22

Environment variables loaded from .env
Prisma schema loaded from prisma\schema.prisma
Datasource "db": PostgreSQL database "tarjama_dev", schema "public" at "localhost:5432"

Error: P1000: Authentication failed against database server at `localhost`, the provided database credentials for `tarjama_user` are not valid.

Please make sure to provide valid database credentials for the database server at `localhost`.
[INFO] Seeding database with sample data...
Environment variables loaded from .env
Running seed command `npx tsx prisma/seed.ts` ...
🌱 Starting database seeding...
❌ Database seeding failed: PrismaClientInitializationError: 
Invalid `prisma.user.upsert()` invocation in
C:\Users\<USER>\OneDrive\Desktop\Tarjama\apps\api\prisma\seed.ts:11:35

   8
   9 // Create admin user
  10 const adminPassword = await bcrypt.hash('admin123', 12);
→ 11 const admin = await prisma.user.upsert(
Authentication failed against database server at `localhost`, the provided database credentials for `tarjama_user` are not valid.

Please make sure to provide valid database credentials for the database server at `localhost`.
    at $n.handleRequestError (C:\Users\<USER>\OneDrive\Desktop\Tarjama\node_modules\@prisma\client\runtime\library.js:121:7615)
    at $n.handleAndLogRequestError (C:\Users\<USER>\OneDrive\Desktop\Tarjama\node_modules\@prisma\client\runtime\library.js:121:6623)
    at $n.request (C:\Users\<USER>\OneDrive\Desktop\Tarjama\node_modules\@prisma\client\runtime\library.js:121:6307)
    at async l (C:\Users\<USER>\OneDrive\Desktop\Tarjama\node_modules\@prisma\client\runtime\library.js:130:9633)
    at async main (C:\Users\<USER>\OneDrive\Desktop\Tarjama\apps\api\prisma\seed.ts:11:17) {
  clientVersion: '5.22.0',
  errorCode: undefined
}

An error occurred while running the seed command:
Error: Command failed with exit code 1: npx tsx prisma/seed.ts
[INFO] Starting development servers...
[SUCCESS] Services will be available at:
  - Web App: http://localhost:3000
  - API: http://localhost:3001
  - API Docs: http://localhost:3001/api/docs
  - MinIO Console: http://localhost:9001

[INFO] Press Ctrl+C to stop all services

[INFO] Servers are starting... Check the URLs above in a few seconds.
Press any key to continue . . . 
> tarjama-ai@1.0.0 dev
> turbo run dev

 WARNING  stale pid file at "C:\\Users\\<USER>\\AppData\\Local\\Temp\\turbod\\584aac7b5b37eee3\\turbod.pid"
• Packages in scope: @tarjama/api, @tarjama/web
• Running dev in 2 packages
• Remote caching disabled
@tarjama/web:dev: cache bypass, force executing 511f5d97ca30584b
@tarjama/web:dev: 
@tarjama/web:dev: > @tarjama/web@1.0.0 dev
@tarjama/web:dev: > next dev
@tarjama/web:dev:
@tarjama/web:dev:   ▲ Next.js 14.2.30
@tarjama/web:dev:   - Local:        http://localhost:3000
@tarjama/web:dev:
@tarjama/web:dev:  ✓ Starting...
@tarjama/web:dev:  ✓ Ready in 1809ms
@tarjama/web:dev:  ○ Compiling / ...
@tarjama/web:dev:  ⨯ ./app/layout.tsx:4:1
@tarjama/web:dev: Module not found: Can't resolve '@/components/ui/toaster'
@tarjama/web:dev:   2 | import { Inter, Noto_Sans_Arabic, JetBrains_Mono } from 'next/font/google';
@tarjama/web:dev:   3 | import { Providers } from './providers';
@tarjama/web:dev: > 4 | import { Toaster } from '@/components/ui/toaster';
@tarjama/web:dev:     | ^
@tarjama/web:dev:   5 | import { Analytics } from '@/components/analytics';
@tarjama/web:dev:   6 | import './globals.css';
@tarjama/web:dev:   7 |
@tarjama/web:dev:
@tarjama/web:dev: https://nextjs.org/docs/messages/module-not-found
@tarjama/web:dev:  ⨯ ./app/layout.tsx:4:1
@tarjama/web:dev: Module not found: Can't resolve '@/components/ui/toaster'
@tarjama/web:dev:   2 | import { Inter, Noto_Sans_Arabic, JetBrains_Mono } from 'next/font/google';
@tarjama/web:dev:   3 | import { Providers } from './providers';
@tarjama/web:dev: > 4 | import { Toaster } from '@/components/ui/toaster';
@tarjama/web:dev:     | ^
@tarjama/web:dev:   5 | import { Analytics } from '@/components/analytics';
@tarjama/web:dev:   6 | import './globals.css';
@tarjama/web:dev:   7 |
@tarjama/web:dev:
@tarjama/web:dev: https://nextjs.org/docs/messages/module-not-found
@tarjama/web:dev:  ⨯ ./app/layout.tsx:4:1
@tarjama/web:dev: Module not found: Can't resolve '@/components/ui/toaster'
@tarjama/web:dev:   2 | import { Inter, Noto_Sans_Arabic, JetBrains_Mono } from 'next/font/google';
@tarjama/web:dev:   3 | import { Providers } from './providers';
@tarjama/web:dev: > 4 | import { Toaster } from '@/components/ui/toaster';
@tarjama/web:dev:     | ^
@tarjama/web:dev:   5 | import { Analytics } from '@/components/analytics';
@tarjama/web:dev:   6 | import './globals.css';
@tarjama/web:dev:   7 |
@tarjama/web:dev:
@tarjama/web:dev: https://nextjs.org/docs/messages/module-not-found
@tarjama/web:dev:  GET / 500 in 4061ms

Terminate batch job (Y/N)? n
PS C:\Users\<USER>\OneDrive\Desktop\Tarjama> 