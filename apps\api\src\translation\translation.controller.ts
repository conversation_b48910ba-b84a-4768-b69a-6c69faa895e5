import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TranslationService, TranslateTextDto } from './translation.service';
import { IsString, IsOptional, Length, IsIn } from 'class-validator';

export class TranslateTextRequestDto {
  @IsString()
  @Length(1, 10000, { message: 'Text must be between 1 and 10,000 characters' })
  text: string;

  @IsString()
  @IsIn(['ar', 'en'], { message: 'Source language must be either "ar" or "en"' })
  sourceLanguage: string;

  @IsString()
  @IsIn(['ar', 'en'], { message: 'Target language must be either "ar" or "en"' })
  targetLanguage: string;

  @IsOptional()
  @IsString()
  @Length(0, 500, { message: 'Context must be less than 500 characters' })
  context?: string;

  @IsOptional()
  @IsString()
  glossaryId?: string;
}

@ApiTags('translations')
@Controller('translations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class TranslationController {
  constructor(private readonly translationService: TranslationService) {}

  @Post('translate')
  @ApiOperation({ 
    summary: 'Translate text',
    description: 'Translate text from Arabic to English or English to Arabic with optional context and glossary support'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Text translated successfully',
    schema: {
      type: 'object',
      properties: {
        translatedText: { type: 'string' },
        sourceLanguage: { type: 'string' },
        targetLanguage: { type: 'string' },
        confidenceScore: { type: 'number' },
        provider: { type: 'string' },
        characterCount: { type: 'number' },
        processingTime: { type: 'number' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  @ApiBody({ type: TranslateTextRequestDto })
  async translateText(
    @Request() req: any,
    @Body(ValidationPipe) translateDto: TranslateTextRequestDto,
  ) {
    return await this.translationService.translateText(req.user.id, translateDto);
  }

  @Get('history')
  @ApiOperation({ 
    summary: 'Get translation history',
    description: 'Retrieve paginated translation history for the current user'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Translation history retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        translations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              sourceText: { type: 'string' },
              translatedText: { type: 'string' },
              sourceLanguage: { type: 'string' },
              targetLanguage: { type: 'string' },
              confidenceScore: { type: 'number' },
              provider: { type: 'string' },
              characterCount: { type: 'number' },
              processingTime: { type: 'number' },
              createdAt: { type: 'string', format: 'date-time' },
              glossary: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                },
              },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  async getTranslationHistory(
    @Request() req: any,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
  ) {
    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 20));
    
    return await this.translationService.getTranslationHistory(
      req.user.id,
      pageNum,
      limitNum,
    );
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get translation by ID',
    description: 'Retrieve a specific translation by its ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Translation retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Translation not found' })
  @ApiParam({ name: 'id', description: 'Translation ID' })
  async getTranslationById(
    @Request() req: any,
    @Param('id') translationId: string,
  ) {
    return await this.translationService.getTranslationById(req.user.id, translationId);
  }
}
