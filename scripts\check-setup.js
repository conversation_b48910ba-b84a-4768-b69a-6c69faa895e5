#!/usr/bin/env node

/**
 * Tarjama.ai Development Environment Check
 * This script verifies that all required tools and dependencies are properly installed
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkCommand(command, name, required = true) {
  try {
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${name}: ${result.trim()}`, 'green');
    return true;
  } catch (error) {
    if (required) {
      log(`❌ ${name}: Not found or not working`, 'red');
      return false;
    } else {
      log(`⚠️  ${name}: Not found (optional)`, 'yellow');
      return true;
    }
  }
}

function checkFile(filePath, name, required = true) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${name}: Found`, 'green');
    return true;
  } else {
    if (required) {
      log(`❌ ${name}: Missing`, 'red');
      return false;
    } else {
      log(`⚠️  ${name}: Missing (optional)`, 'yellow');
      return true;
    }
  }
}

function main() {
  log('🔍 Tarjama.ai Development Environment Check', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  let allGood = true;
  
  // Check required tools
  log('\n📋 Checking Required Tools:', 'blue');
  allGood &= checkCommand('node --version', 'Node.js');
  allGood &= checkCommand('npm --version', 'npm');
  allGood &= checkCommand('docker --version', 'Docker');
  allGood &= checkCommand('docker-compose --version', 'Docker Compose');
  
  // Check optional tools
  log('\n🛠️  Checking Optional Tools:', 'blue');
  checkCommand('git --version', 'Git', false);
  checkCommand('make --version', 'Make', false);
  
  // Check project files
  log('\n📁 Checking Project Files:', 'blue');
  allGood &= checkFile('package.json', 'Root package.json');
  allGood &= checkFile('apps/api/package.json', 'API package.json');
  allGood &= checkFile('apps/web/package.json', 'Web package.json');
  allGood &= checkFile('docker-compose.yml', 'Docker Compose config');
  allGood &= checkFile('turbo.json', 'Turbo config');
  
  // Check environment file
  log('\n🔧 Checking Environment:', 'blue');
  if (checkFile('.env', 'Environment file', false)) {
    log('   💡 Environment file found - you can customize settings', 'cyan');
  } else {
    log('   💡 No .env file - will use defaults from .env.example', 'cyan');
  }
  
  // Check if dependencies are installed
  log('\n📦 Checking Dependencies:', 'blue');
  const nodeModulesExists = checkFile('node_modules', 'Root dependencies', false);
  const apiNodeModulesExists = checkFile('apps/api/node_modules', 'API dependencies', false);
  const webNodeModulesExists = checkFile('apps/web/node_modules', 'Web dependencies', false);
  
  if (!nodeModulesExists || !apiNodeModulesExists || !webNodeModulesExists) {
    log('   💡 Run "npm install" to install dependencies', 'cyan');
  }
  
  // Check Docker services
  log('\n🐳 Checking Docker Services:', 'blue');
  try {
    const dockerPs = execSync('docker-compose ps', { encoding: 'utf8', stdio: 'pipe' });
    if (dockerPs.includes('postgres') && dockerPs.includes('Up')) {
      log('✅ PostgreSQL: Running', 'green');
    } else {
      log('⚠️  PostgreSQL: Not running', 'yellow');
      log('   💡 Run "docker-compose up -d postgres redis minio" to start services', 'cyan');
    }
  } catch (error) {
    log('⚠️  Docker services: Not running', 'yellow');
    log('   💡 Run "docker-compose up -d postgres redis minio" to start services', 'cyan');
  }
  
  // Summary
  log('\n📊 Summary:', 'magenta');
  if (allGood) {
    log('🎉 All required tools and files are present!', 'green');
    log('\n🚀 Next Steps:', 'blue');
    log('1. Run "npm install" if dependencies are not installed', 'cyan');
    log('2. Run "docker-compose up -d postgres redis minio" to start services', 'cyan');
    log('3. Run "npm run dev" to start the development servers', 'cyan');
    log('\nOr use the automated setup:', 'cyan');
    log('- Windows: scripts\\dev-setup.bat', 'cyan');
    log('- Linux/macOS: ./scripts/dev-setup.sh', 'cyan');
  } else {
    log('❌ Some required tools or files are missing.', 'red');
    log('Please install the missing requirements and try again.', 'red');
  }
  
  log('\n📚 For detailed setup instructions, see GETTING_STARTED.md', 'blue');
}

if (require.main === module) {
  main();
}

module.exports = { checkCommand, checkFile };
