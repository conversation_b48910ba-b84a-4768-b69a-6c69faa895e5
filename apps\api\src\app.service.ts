import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from './prisma/prisma.service';
import { RedisService } from './redis/redis.service';

@Injectable()
export class AppService {
  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
  ) {}

  getAppInfo() {
    return {
      name: 'Tarjama.ai API',
      version: '1.0.0',
      description: 'AI-powered Arabic-English translation platform for construction professionals',
      environment: this.configService.get('NODE_ENV', 'development'),
      timestamp: new Date().toISOString(),
    };
  }

  getHealth() {
    const memoryUsage = process.memoryUsage();
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
      },
      pid: process.pid,
      version: process.version,
    };
  }

  async getReadiness() {
    const services = {
      database: 'unknown',
      redis: 'unknown',
      storage: 'unknown',
    };

    try {
      // Check database connection
      await this.prismaService.$queryRaw`SELECT 1`;
      services.database = 'connected';
    } catch (error) {
      services.database = 'disconnected';
    }

    try {
      // Check Redis connection
      await this.redisService.ping();
      services.redis = 'connected';
    } catch (error) {
      services.redis = 'disconnected';
    }

    // For now, assume storage is always available
    // In production, you would check S3/MinIO connectivity
    services.storage = 'connected';

    const isReady = Object.values(services).every(status => status === 'connected');

    return {
      status: isReady ? 'ready' : 'not_ready',
      timestamp: new Date().toISOString(),
      services,
    };
  }
}
