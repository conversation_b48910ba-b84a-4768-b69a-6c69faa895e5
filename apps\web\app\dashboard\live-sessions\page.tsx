"use client";

import { useState } from "react";
import Link from "next/link";

interface LiveSession {
  id: string;
  title: string;
  description: string;
  status: "active" | "scheduled" | "completed";
  participants: number;
  maxParticipants: number;
  startTime: string;
  endTime?: string;
  duration?: number;
  languages: string[];
  recordingUrl?: string;
  transcriptUrl?: string;
}

export default function LiveSessionsPage() {
  const [sessions] = useState<LiveSession[]>([
    {
      id: "1",
      title: "Site Safety Meeting",
      description: "Weekly safety briefing for construction team",
      status: "active",
      participants: 8,
      maxParticipants: 15,
      startTime: "2024-01-15T14:00:00Z",
      languages: ["ar", "en"],
    },
    {
      id: "2",
      title: "Project Planning Session",
      description: "Quarterly project planning and resource allocation",
      status: "scheduled",
      participants: 0,
      maxParticipants: 20,
      startTime: "2024-01-16T09:00:00Z",
      languages: ["ar", "en"],
    },
    {
      id: "3",
      title: "Client Presentation",
      description: "Project progress presentation to stakeholders",
      status: "completed",
      participants: 12,
      maxParticipants: 12,
      startTime: "2024-01-14T10:00:00Z",
      endTime: "2024-01-14T11:30:00Z",
      duration: 90,
      languages: ["ar", "en"],
      recordingUrl: "/recordings/session-3.mp4",
      transcriptUrl: "/transcripts/session-3.txt",
    },
  ]);

  const [filter, setFilter] = useState<"all" | "active" | "scheduled" | "completed">("all");

  const filteredSessions = sessions.filter((session) => 
    filter === "all" || session.status === filter
  );

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-900/50 text-green-300";
      case "scheduled": return "bg-blue-900/50 text-blue-300";
      case "completed": return "bg-gray-900/50 text-gray-300";
      default: return "bg-gray-900/50 text-gray-300";
    }
  };

  const getLanguageFlag = (lang: string) => {
    return lang === "ar" ? "🇸🇦" : "🇺🇸";
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white">Live Translation Sessions</h1>
              <p className="text-gray-400 mt-2">Manage real-time translation sessions for meetings and calls</p>
            </div>
            <button className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
              Start New Session
            </button>
          </div>

          {/* Filters */}
          <div className="flex gap-2">
            {["all", "active", "scheduled", "completed"].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors capitalize ${
                  filter === status
                    ? "bg-white text-black"
                    : "bg-gray-800 text-gray-300 hover:bg-gray-700"
                }`}
              >
                {status}
              </button>
            ))}
          </div>
        </div>

        {/* Sessions Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredSessions.map((session) => (
            <div
              key={session.id}
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 hover:bg-gray-900/70 transition-all duration-200"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-2">{session.title}</h3>
                  <p className="text-sm text-gray-400 mb-3">{session.description}</p>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                  {session.status}
                </span>
              </div>

              {/* Session Info */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Start Time</span>
                  <span className="text-white">{formatDateTime(session.startTime)}</span>
                </div>

                {session.endTime && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">End Time</span>
                    <span className="text-white">{formatDateTime(session.endTime)}</span>
                  </div>
                )}

                {session.duration && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Duration</span>
                    <span className="text-white">{session.duration} minutes</span>
                  </div>
                )}

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Participants</span>
                  <span className="text-white">
                    {session.participants}/{session.maxParticipants}
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Languages</span>
                  <div className="flex items-center gap-1">
                    {session.languages.map((lang) => (
                      <span key={lang} className="text-lg">
                        {getLanguageFlag(lang)}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Progress Bar for Active Sessions */}
              {session.status === "active" && (
                <div className="mb-6">
                  <div className="flex items-center justify-between text-xs text-gray-400 mb-2">
                    <span>Session Progress</span>
                    <span>45 min elapsed</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: "60%" }}></div>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center gap-2">
                {session.status === "active" && (
                  <>
                    <button className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
                      Join Session
                    </button>
                    <button className="bg-gray-800 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </button>
                  </>
                )}

                {session.status === "scheduled" && (
                  <>
                    <button className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                      Edit Session
                    </button>
                    <button className="bg-gray-800 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </>
                )}

                {session.status === "completed" && (
                  <>
                    <button className="flex-1 bg-gray-700 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-600 transition-colors">
                      View Details
                    </button>
                    {session.recordingUrl && (
                      <button className="bg-gray-800 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>
                    )}
                    {session.transcriptUrl && (
                      <button className="bg-gray-800 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredSessions.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎤</div>
            <h3 className="text-xl font-semibold text-white mb-2">No sessions found</h3>
            <p className="text-gray-400 mb-6">
              {filter === "all" 
                ? "Start your first live translation session" 
                : `No ${filter} sessions at the moment`
              }
            </p>
            <button className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
              Start New Session
            </button>
          </div>
        )}

        {/* Quick Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">
              {sessions.filter(s => s.status === "active").length}
            </div>
            <div className="text-gray-400">Active Sessions</div>
          </div>
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">
              {sessions.filter(s => s.status === "scheduled").length}
            </div>
            <div className="text-gray-400">Scheduled</div>
          </div>
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">
              {sessions.filter(s => s.status === "completed").length}
            </div>
            <div className="text-gray-400">Completed</div>
          </div>
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">
              {sessions.reduce((total, s) => total + (s.duration || 0), 0)}
            </div>
            <div className="text-gray-400">Total Minutes</div>
          </div>
        </div>
      </div>
    </div>
  );
}
