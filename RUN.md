# 🚀 Tarjama.ai - Complete Setup & Run Guide

This guide will help you run the entire Tarjama.ai platform including frontend, backend, database, authentication, and all services.

## 📋 Prerequisites

Before starting, ensure you have the following installed:

- **Node.js** (v18 or higher) - [Download](https://nodejs.org/)
- **Docker & Docker Compose** - [Download](https://www.docker.com/products/docker-desktop/)
- **Git** - [Download](https://git-scm.com/)

## 🏗️ Project Architecture

The Tarjama.ai platform consists of:

- **Frontend**: Next.js 14 web application (Port 3000)
- **Backend**: NestJS API server (Port 3001)
- **Database**: PostgreSQL (Port 5432)
- **Cache**: Redis (Port 6379)
- **Storage**: MinIO S3-compatible storage (Port 9000)
- **Proxy**: Nginx reverse proxy (Port 80/443)

## ⚡ Quick Start (Single Command)

### Option 1: Frontend Only (Fastest - No Docker Required)

```bash
# Install dependencies and start frontend
npm install
npm run dev:web
```

**Access**: http://localhost:3000 (Works with demo mode & fallback features)

### Option 2: Full Stack with Docker (Recommended)

```bash
# Prerequisites: Docker Desktop must be running
# Copy environment file
cp .env.example .env

# Run everything with Docker
docker-compose up --build
```

### Option 3: Development Mode (Local Services)

```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env

# Start database services (requires Docker)
npm run services:start

# Start frontend and backend
npm run dev:all
```

## 🔧 Detailed Setup Instructions

### Step 1: Environment Configuration

1. **Copy the environment file:**

   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file with your configurations:**

   ```bash
   # Required for basic functionality
   DATABASE_URL="postgresql://tarjama_user:tarjama_password@localhost:5432/tarjama_dev"
   JWT_SECRET="your-super-secret-jwt-key-change-in-production"
   NEXTAUTH_SECRET="your-super-secret-nextauth-key-change-in-production"

   # Optional API keys (use demo mode if not provided)
   OPENAI_API_KEY="your-openai-api-key"
   DEEPL_API_KEY="your-deepl-api-key"
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"
   ```

### Step 2: Install Dependencies

```bash
# Install root dependencies
npm install

# Install frontend dependencies
cd apps/web && npm install && cd ../..

# Install backend dependencies
cd apps/api && npm install && cd ../..
```

### Step 3: Database Setup

#### Option A: Using Docker (Recommended)

```bash
# Start database services
docker-compose up postgres redis minio -d

# Wait for services to be ready (30 seconds)
sleep 30

# Setup database schema
cd apps/api
npm run db:generate
npm run db:push
npm run db:seed
cd ../..
```

#### Option B: Local Database

```bash
# Install PostgreSQL and Redis locally
# Update .env with your local database URLs

# Setup database
cd apps/api
npm run db:generate
npm run db:migrate
npm run db:seed
cd ../..
```

## 🚀 Running the Application

### Method 1: Docker Compose (Full Stack)

```bash
# Start all services
docker-compose up --build

# Or run in background
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

**Access URLs:**

- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Database: localhost:5432
- Redis: localhost:6379
- MinIO: http://localhost:9000
- Nginx: http://localhost:80

### Method 2: Development Mode (Local)

```bash
# Terminal 1: Start database services
docker-compose up postgres redis minio -d

# Terminal 2: Start backend API
cd apps/api
npm run start:dev

# Terminal 3: Start frontend
cd apps/web
npm run dev

# Terminal 4: (Optional) Start additional services
npm run dev:services
```

### Method 3: Production Mode

```bash
# Build all applications
npm run build

# Start in production mode
docker-compose -f docker-compose.prod.yml up --build -d
```

## 🧪 Demo Credentials

Use these credentials to test the application:

- **Email**: `<EMAIL>`
- **Password**: `demo123`

## 📊 Available Services & Ports

| Service       | Port | URL                   | Description       |
| ------------- | ---- | --------------------- | ----------------- |
| Frontend      | 3000 | http://localhost:3000 | Next.js Web App   |
| Backend API   | 3001 | http://localhost:3001 | NestJS API Server |
| PostgreSQL    | 5432 | localhost:5432        | Main Database     |
| Redis         | 6379 | localhost:6379        | Cache & Sessions  |
| MinIO         | 9000 | http://localhost:9000 | File Storage      |
| MinIO Console | 9001 | http://localhost:9001 | Storage Admin     |
| Nginx         | 80   | http://localhost      | Reverse Proxy     |

## 🔍 Health Checks & Monitoring

```bash
# Check service health
curl http://localhost:3001/health

# View API documentation
open http://localhost:3001/api

# Monitor logs
docker-compose logs -f [service-name]

# Database admin
cd apps/api && npm run db:studio
```

## 🛠️ Development Commands

```bash
# Frontend development
cd apps/web
npm run dev          # Start dev server
npm run build        # Build for production
npm run lint         # Run linting
npm run test         # Run tests

# Backend development
cd apps/api
npm run start:dev    # Start with hot reload
npm run start:debug  # Start with debugging
npm run test         # Run tests
npm run db:studio    # Open database admin

# Database operations
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema changes
npm run db:migrate   # Run migrations
npm run db:seed      # Seed database
```

## 🐛 Troubleshooting

### Common Issues:

1. **Docker Desktop not running:**

   ```bash
   # Start Docker Desktop first, then:
   npm run services:start

   # Or run frontend only (no Docker needed):
   npm run dev:web
   ```

2. **Port already in use:**

   ```bash
   # Kill processes on ports
   npx kill-port 3000 3001 5432 6379 9000

   # Or use different ports in .env file
   ```

3. **Database connection failed:**

   ```bash
   # Restart database services
   docker-compose restart postgres redis

   # Or use demo mode (no database needed)
   npm run dev:web
   ```

4. **Dependencies issues:**

   ```bash
   # Clean install
   rm -rf node_modules apps/*/node_modules
   npm install
   ```

5. **Docker issues:**

   ```bash
   # Clean Docker
   docker-compose down -v
   docker system prune -f
   docker-compose up --build
   ```

6. **API connection errors:**
   ```bash
   # The app works in demo mode without backend
   # Just run: npm run dev:web
   # All features have fallback functionality
   ```

## 🎯 Testing the Application

1. **Visit Homepage**: http://localhost:3000
2. **Try Translation Demo**: http://localhost:3000/demo
3. **Login with Demo**: http://localhost:3000/login
4. **Explore Dashboard**: http://localhost:3000/dashboard/overview
5. **Test File Upload**: http://localhost:3000/dashboard/documents

## 📝 Additional Notes

- The application works in **demo mode** without API keys
- All features have **fallback functionality** for offline development
- **Hot reload** is enabled for both frontend and backend
- **Database seeding** creates sample data for testing
- **Docker volumes** persist data between restarts

## 🎉 Success!

If everything is running correctly, you should see:

- ✅ Frontend at http://localhost:3000
- ✅ Backend API at http://localhost:3001
- ✅ All services healthy
- ✅ Demo login working
- ✅ Translation features functional

**Happy coding! 🚀**
