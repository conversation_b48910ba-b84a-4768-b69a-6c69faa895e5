"use client";

import { useState } from "react";

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<"general" | "translation" | "notifications" | "billing" | "security">("general");
  
  const [settings, setSettings] = useState({
    general: {
      language: "en",
      timezone: "UTC+3",
      theme: "dark",
      autoSave: true,
    },
    translation: {
      defaultSourceLang: "ar",
      defaultTargetLang: "en",
      autoDetectLanguage: true,
      saveToGlossary: true,
      qualityLevel: "high",
      enableSpellCheck: true,
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      sessionReminders: true,
      weeklyReports: true,
      securityAlerts: true,
    },
    billing: {
      plan: "Professional",
      billingCycle: "monthly",
      autoRenew: true,
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: "30",
      loginAlerts: true,
    },
  });

  const tabs = [
    { id: "general", name: "General", icon: "⚙️" },
    { id: "translation", name: "Translation", icon: "🌐" },
    { id: "notifications", name: "Notifications", icon: "🔔" },
    { id: "billing", name: "Billing", icon: "💳" },
    { id: "security", name: "Security", icon: "🔒" },
  ];

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">Settings</h1>
          <p className="text-gray-400 mt-2">Manage your account preferences and configuration</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    activeTab === tab.id
                      ? "bg-white text-black"
                      : "bg-gray-900/50 text-gray-300 hover:bg-gray-800"
                  }`}
                >
                  <span className="text-lg">{tab.icon}</span>
                  <span className="font-medium">{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-8">
              {/* General Settings */}
              {activeTab === "general" && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">General Settings</h2>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Interface Language
                      </label>
                      <select
                        value={settings.general.language}
                        onChange={(e) => updateSetting("general", "language", e.target.value)}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      >
                        <option value="en">English</option>
                        <option value="ar">العربية</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Timezone
                      </label>
                      <select
                        value={settings.general.timezone}
                        onChange={(e) => updateSetting("general", "timezone", e.target.value)}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      >
                        <option value="UTC+3">UTC+3 (Riyadh, Kuwait)</option>
                        <option value="UTC+4">UTC+4 (Dubai, Abu Dhabi)</option>
                        <option value="UTC+2">UTC+2 (Cairo)</option>
                      </select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-white font-medium">Auto-save translations</h3>
                        <p className="text-gray-400 text-sm">Automatically save translations to your history</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.general.autoSave}
                          onChange={(e) => updateSetting("general", "autoSave", e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {/* Translation Settings */}
              {activeTab === "translation" && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">Translation Settings</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Default Source Language
                        </label>
                        <select
                          value={settings.translation.defaultSourceLang}
                          onChange={(e) => updateSetting("translation", "defaultSourceLang", e.target.value)}
                          className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                        >
                          <option value="ar">Arabic</option>
                          <option value="en">English</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Default Target Language
                        </label>
                        <select
                          value={settings.translation.defaultTargetLang}
                          onChange={(e) => updateSetting("translation", "defaultTargetLang", e.target.value)}
                          className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                        >
                          <option value="en">English</option>
                          <option value="ar">Arabic</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Translation Quality
                      </label>
                      <select
                        value={settings.translation.qualityLevel}
                        onChange={(e) => updateSetting("translation", "qualityLevel", e.target.value)}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      >
                        <option value="fast">Fast (Lower accuracy)</option>
                        <option value="balanced">Balanced</option>
                        <option value="high">High Quality (Slower)</option>
                      </select>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium">Auto-detect language</h3>
                          <p className="text-gray-400 text-sm">Automatically detect source language</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.translation.autoDetectLanguage}
                            onChange={(e) => updateSetting("translation", "autoDetectLanguage", e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium">Save to glossary</h3>
                          <p className="text-gray-400 text-sm">Automatically add new terms to your glossary</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.translation.saveToGlossary}
                            onChange={(e) => updateSetting("translation", "saveToGlossary", e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Notifications Settings */}
              {activeTab === "notifications" && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">Notification Settings</h2>
                  <div className="space-y-6">
                    {Object.entries(settings.notifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </h3>
                          <p className="text-gray-400 text-sm">
                            {key === "emailNotifications" && "Receive updates via email"}
                            {key === "pushNotifications" && "Browser push notifications"}
                            {key === "sessionReminders" && "Reminders for scheduled sessions"}
                            {key === "weeklyReports" && "Weekly usage reports"}
                            {key === "securityAlerts" && "Security and login alerts"}
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => updateSetting("notifications", key, e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Billing Settings */}
              {activeTab === "billing" && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">Billing & Subscription</h2>
                  <div className="space-y-6">
                    <div className="bg-gray-800/50 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-semibold text-white">Professional Plan</h3>
                          <p className="text-gray-400">$49/month • Billed monthly</p>
                        </div>
                        <span className="bg-green-900/50 text-green-300 px-3 py-1 rounded-full text-sm">Active</span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-400">Next billing date:</span>
                          <div className="text-white font-medium">Feb 15, 2024</div>
                        </div>
                        <div>
                          <span className="text-gray-400">Payment method:</span>
                          <div className="text-white font-medium">•••• 4242</div>
                        </div>
                        <div>
                          <span className="text-gray-400">Usage this month:</span>
                          <div className="text-white font-medium">1,247 translations</div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <button className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        Upgrade Plan
                      </button>
                      <button className="bg-gray-800 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                        Update Payment
                      </button>
                      <button className="text-red-400 hover:text-red-300 transition-colors">
                        Cancel Subscription
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === "security" && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">Security Settings</h2>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-white font-medium">Two-Factor Authentication</h3>
                        <p className="text-gray-400 text-sm">Add an extra layer of security to your account</p>
                      </div>
                      <button className="bg-white text-black px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        Enable 2FA
                      </button>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Session Timeout (minutes)
                      </label>
                      <select
                        value={settings.security.sessionTimeout}
                        onChange={(e) => updateSetting("security", "sessionTimeout", e.target.value)}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-white/20"
                      >
                        <option value="15">15 minutes</option>
                        <option value="30">30 minutes</option>
                        <option value="60">1 hour</option>
                        <option value="120">2 hours</option>
                      </select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-white font-medium">Login alerts</h3>
                        <p className="text-gray-400 text-sm">Get notified of new login attempts</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.security.loginAlerts}
                          onChange={(e) => updateSetting("security", "loginAlerts", e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="border-t border-gray-700 pt-6">
                      <h3 className="text-white font-medium mb-4">Danger Zone</h3>
                      <div className="space-y-3">
                        <button className="w-full bg-red-900/20 border border-red-800 text-red-400 px-4 py-3 rounded-lg hover:bg-red-900/30 transition-colors text-left">
                          Change Password
                        </button>
                        <button className="w-full bg-red-900/20 border border-red-800 text-red-400 px-4 py-3 rounded-lg hover:bg-red-900/30 transition-colors text-left">
                          Delete Account
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="mt-8 pt-6 border-t border-gray-700">
                <div className="flex items-center justify-between">
                  <p className="text-gray-400 text-sm">Changes are saved automatically</p>
                  <button className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
