# Tarjama.ai Development Makefile

.PHONY: help install dev build test clean docker-up docker-down db-setup db-reset

# Default target
help:
	@echo "Tarjama.ai Development Commands"
	@echo "==============================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install     - Install all dependencies"
	@echo "  setup       - Full development environment setup"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev         - Start development servers"
	@echo "  build       - Build all applications"
	@echo "  test        - Run all tests"
	@echo "  lint        - Run linting"
	@echo "  type-check  - Run TypeScript type checking"
	@echo ""
	@echo "Docker Commands:"
	@echo "  docker-up   - Start Docker services"
	@echo "  docker-down - Stop Docker services"
	@echo "  docker-logs - View Docker logs"
	@echo ""
	@echo "Database Commands:"
	@echo "  db-setup    - Setup database schema and seed data"
	@echo "  db-reset    - Reset database (drop and recreate)"
	@echo "  db-studio   - Open Prisma Studio"
	@echo "  db-migrate  - Run database migrations"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean       - Clean build artifacts and node_modules"
	@echo "  logs        - View application logs"

# Installation
install:
	@echo "📦 Installing dependencies..."
	npm install

# Full setup
setup: install docker-up db-setup
	@echo "✅ Development environment setup complete!"
	@echo ""
	@echo "🚀 You can now run 'make dev' to start the development servers"

# Development
dev:
	@echo "🚀 Starting development servers..."
	npm run dev

build:
	@echo "🔨 Building applications..."
	npm run build

test:
	@echo "🧪 Running tests..."
	npm run test

lint:
	@echo "🔍 Running linting..."
	npm run lint

type-check:
	@echo "📝 Running TypeScript type checking..."
	npm run type-check

# Docker
docker-up:
	@echo "🐳 Starting Docker services..."
	docker-compose up -d postgres redis minio
	@echo "⏳ Waiting for services to be ready..."
	sleep 10

docker-down:
	@echo "🛑 Stopping Docker services..."
	docker-compose down

docker-logs:
	@echo "📋 Viewing Docker logs..."
	docker-compose logs -f

# Database
db-setup:
	@echo "🗄️ Setting up database..."
	cd apps/api && npx prisma generate
	cd apps/api && npx prisma db push
	cd apps/api && npx prisma db seed

db-reset:
	@echo "🔄 Resetting database..."
	cd apps/api && npx prisma migrate reset --force

db-studio:
	@echo "🎨 Opening Prisma Studio..."
	cd apps/api && npx prisma studio

db-migrate:
	@echo "📊 Running database migrations..."
	cd apps/api && npx prisma migrate dev

# Utility
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf node_modules
	rm -rf apps/*/node_modules
	rm -rf apps/*/.next
	rm -rf apps/*/dist
	rm -rf .turbo

logs:
	@echo "📋 Viewing application logs..."
	docker-compose logs -f api web

# Quick development workflow
quick-start: docker-up
	@echo "⚡ Quick starting development environment..."
	sleep 5
	npm run dev

# Production build
prod-build:
	@echo "🏭 Building for production..."
	NODE_ENV=production npm run build

# Health check
health:
	@echo "🏥 Checking service health..."
	@curl -s http://localhost:3001/api/v1/health || echo "❌ API not responding"
	@curl -s http://localhost:3000 || echo "❌ Web app not responding"

# Show running services
status:
	@echo "📊 Service Status:"
	@echo "=================="
	@docker-compose ps

# Backup database
db-backup:
	@echo "💾 Creating database backup..."
	docker-compose exec postgres pg_dump -U tarjama_user tarjama_dev > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Restore database
db-restore:
	@echo "📥 Restoring database..."
	@read -p "Enter backup file path: " backup_file; \
	docker-compose exec -T postgres psql -U tarjama_user -d tarjama_dev < $$backup_file
