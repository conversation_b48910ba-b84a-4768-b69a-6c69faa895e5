import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { LiveSessionStatus } from "@prisma/client";

export interface CreateSessionDto {
  name: string;
  description?: string;
  sourceLanguage: string;
  targetLanguage: string;
  maxParticipants?: number;
  isPublic?: boolean;
}

export interface JoinSessionDto {
  sessionId: string;
  participantName?: string;
}

export interface TranslateMessageDto {
  sessionId: string;
  message: string;
  sourceLanguage: string;
  targetLanguage: string;
}

export interface SessionMessage {
  id: string;
  sessionId: string;
  userId: string;
  userName: string;
  originalMessage: string;
  translatedMessage?: string;
  sourceLanguage: string;
  targetLanguage?: string;
  timestamp: Date;
}

@Injectable()
export class LiveTranslationService {
  private readonly logger = new Logger(LiveTranslationService.name);
  private readonly activeSessions = new Map<string, any>();
  private readonly sessionParticipants = new Map<string, Set<string>>();

  constructor(private readonly prisma: PrismaService) {}

  async createSession(userId: string, createDto: CreateSessionDto) {
    try {
      // Validate languages
      if (
        !["ar", "en"].includes(createDto.sourceLanguage) ||
        !["ar", "en"].includes(createDto.targetLanguage)
      ) {
        throw new BadRequestException(
          "Only Arabic (ar) and English (en) languages are supported"
        );
      }

      if (createDto.sourceLanguage === createDto.targetLanguage) {
        throw new BadRequestException(
          "Source and target languages must be different"
        );
      }

      const session = await this.prisma.liveSession.create({
        data: {
          userId,
          name: createDto.name,
          description: createDto.description,
          sourceLanguage: createDto.sourceLanguage,
          targetLanguage: createDto.targetLanguage,
          maxParticipants: createDto.maxParticipants || 10,
          isPublic: createDto.isPublic || false,
          status: LiveSessionStatus.ACTIVE,
          metadata: {
            createdAt: new Date().toISOString(),
            participantCount: 0,
            messageCount: 0,
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Initialize session in memory
      this.activeSessions.set(session.id, {
        ...session,
        participants: new Set([userId]),
        messages: [],
      });
      this.sessionParticipants.set(session.id, new Set([userId]));

      this.logger.log(`Live session created: ${session.id} by user ${userId}`);
      return session;
    } catch (error) {
      this.logger.error(
        `Error creating live session for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  async joinSession(userId: string, joinDto: JoinSessionDto) {
    try {
      const session = await this.prisma.liveSession.findUnique({
        where: { id: joinDto.sessionId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!session) {
        throw new NotFoundException("Session not found");
      }

      if (session.status !== LiveSessionStatus.ACTIVE) {
        throw new BadRequestException("Session is not active");
      }

      // Check if session is public or user has access
      if (!session.isPublic && session.userId !== userId) {
        throw new BadRequestException("You do not have access to this session");
      }

      // Check participant limit
      const currentParticipants =
        this.sessionParticipants.get(session.id) || new Set();
      if (currentParticipants.size >= session.maxParticipants) {
        throw new BadRequestException("Session is full");
      }

      // Add participant
      currentParticipants.add(userId);
      this.sessionParticipants.set(session.id, currentParticipants);

      // Update session in memory
      const activeSession = this.activeSessions.get(session.id);
      if (activeSession) {
        activeSession.participants.add(userId);
      }

      // Update participant count in database
      await this.prisma.liveSession.update({
        where: { id: session.id },
        data: {
          metadata: {
            ...(session.metadata as any),
            participantCount: currentParticipants.size,
          } as any,
        },
      });

      this.logger.log(`User ${userId} joined session ${session.id}`);
      return {
        session,
        participantCount: currentParticipants.size,
      };
    } catch (error) {
      this.logger.error(
        `Error joining session ${joinDto.sessionId} for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  async leaveSession(userId: string, sessionId: string) {
    try {
      const participants = this.sessionParticipants.get(sessionId);
      if (participants) {
        participants.delete(userId);

        // Update session in memory
        const activeSession = this.activeSessions.get(sessionId);
        if (activeSession) {
          activeSession.participants.delete(userId);
        }

        // Update participant count in database
        const session = await this.prisma.liveSession.findUnique({
          where: { id: sessionId },
        });

        if (session) {
          await this.prisma.liveSession.update({
            where: { id: sessionId },
            data: {
              metadata: {
                ...(session.metadata as any),
                participantCount: participants.size,
              } as any,
            },
          });
        }

        this.logger.log(`User ${userId} left session ${sessionId}`);
      }
    } catch (error) {
      this.logger.error(
        `Error leaving session ${sessionId} for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  async translateMessage(
    userId: string,
    translateDto: TranslateMessageDto
  ): Promise<SessionMessage> {
    try {
      // Check if user is in session
      const participants = this.sessionParticipants.get(translateDto.sessionId);
      if (!participants || !participants.has(userId)) {
        throw new BadRequestException(
          "You are not a participant in this session"
        );
      }

      // Get user info
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, name: true },
      });

      if (!user) {
        throw new NotFoundException("User not found");
      }

      // Mock translation (replace with actual translation service)
      const translatedMessage = await this.performLiveTranslation(
        translateDto.message,
        translateDto.sourceLanguage,
        translateDto.targetLanguage
      );

      // Create message object
      const message: SessionMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        sessionId: translateDto.sessionId,
        userId,
        userName: user.name,
        originalMessage: translateDto.message,
        translatedMessage,
        sourceLanguage: translateDto.sourceLanguage,
        targetLanguage: translateDto.targetLanguage,
        timestamp: new Date(),
      };

      // Add to session messages in memory
      const activeSession = this.activeSessions.get(translateDto.sessionId);
      if (activeSession) {
        activeSession.messages.push(message);

        // Keep only last 100 messages in memory
        if (activeSession.messages.length > 100) {
          activeSession.messages = activeSession.messages.slice(-100);
        }
      }

      // Update message count in database
      const session = await this.prisma.liveSession.findUnique({
        where: { id: translateDto.sessionId },
      });

      if (session) {
        await this.prisma.liveSession.update({
          where: { id: translateDto.sessionId },
          data: {
            metadata: {
              ...(session.metadata as any),
              messageCount: ((session.metadata as any)?.messageCount || 0) + 1,
            } as any,
          },
        });
      }

      this.logger.log(
        `Message translated in session ${translateDto.sessionId} by user ${userId}`
      );
      return message;
    } catch (error) {
      this.logger.error(
        `Error translating message in session ${translateDto.sessionId} for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  async getSessionMessages(
    userId: string,
    sessionId: string,
    limit: number = 50
  ) {
    try {
      // Check if user is in session
      const participants = this.sessionParticipants.get(sessionId);
      if (!participants || !participants.has(userId)) {
        throw new BadRequestException(
          "You are not a participant in this session"
        );
      }

      const activeSession = this.activeSessions.get(sessionId);
      if (!activeSession) {
        return { messages: [] };
      }

      // Return last N messages
      const messages = activeSession.messages.slice(-limit);

      return {
        messages,
        sessionId,
        participantCount: participants.size,
      };
    } catch (error) {
      this.logger.error(
        `Error getting messages for session ${sessionId} for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  async getUserSessions(userId: string, page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;

      const [sessions, total] = await Promise.all([
        this.prisma.liveSession.findMany({
          where: {
            OR: [{ userId }, { isPublic: true }],
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        }),
        this.prisma.liveSession.count({
          where: {
            OR: [{ userId }, { isPublic: true }],
          },
        }),
      ]);

      return {
        sessions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Error getting sessions for user ${userId}:`, error);
      throw error;
    }
  }

  async endSession(userId: string, sessionId: string) {
    try {
      const session = await this.prisma.liveSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        throw new NotFoundException("Session not found");
      }

      if (session.userId !== userId) {
        throw new BadRequestException(
          "Only the session owner can end the session"
        );
      }

      // Update session status
      await this.prisma.liveSession.update({
        where: { id: sessionId },
        data: {
          status: LiveSessionStatus.ENDED,
          endedAt: new Date(),
        },
      });

      // Clean up memory
      this.activeSessions.delete(sessionId);
      this.sessionParticipants.delete(sessionId);

      this.logger.log(`Session ended: ${sessionId} by user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Error ending session ${sessionId} for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  private async performLiveTranslation(
    text: string,
    sourceLanguage: string,
    targetLanguage: string
  ): Promise<string> {
    // Mock translation - replace with actual AI service
    const isArabicToEnglish =
      sourceLanguage === "ar" && targetLanguage === "en";
    const isEnglishToArabic =
      sourceLanguage === "en" && targetLanguage === "ar";

    if (isArabicToEnglish) {
      return `[EN] ${text}`;
    } else if (isEnglishToArabic) {
      return `[AR] ${text}`;
    } else {
      return `[${targetLanguage.toUpperCase()}] ${text}`;
    }
  }
}
