"use client";

import { useState } from "react";

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d" | "1y">("30d");

  const stats = {
    totalTranslations: 1247,
    totalCharacters: 89432,
    totalSessions: 23,
    totalMinutes: 456,
    averageAccuracy: 99.2,
    topLanguages: [
      { language: "Arabic → English", count: 743, percentage: 59.6 },
      { language: "English → Arabic", count: 504, percentage: 40.4 },
    ],
    dailyUsage: [
      { date: "Jan 1", translations: 45, characters: 3200 },
      { date: "Jan 2", translations: 52, characters: 3800 },
      { date: "Jan 3", translations: 38, characters: 2900 },
      { date: "Jan 4", translations: 61, characters: 4100 },
      { date: "Jan 5", translations: 49, characters: 3500 },
      { date: "Jan 6", translations: 55, characters: 3900 },
      { date: "Jan 7", translations: 43, characters: 3100 },
    ],
    topTerms: [
      { term: "خرسانة (Concrete)", count: 89 },
      { term: "حديد التسليح (Rebar)", count: 76 },
      { term: "مواد البناء (Building Materials)", count: 65 },
      { term: "السلامة (Safety)", count: 54 },
      { term: "مقاول (Contractor)", count: 43 },
    ],
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white">Analytics</h1>
              <p className="text-gray-400 mt-2">Track your translation usage and performance</p>
            </div>
            <div className="flex items-center gap-2">
              {["7d", "30d", "90d", "1y"].map((range) => (
                <button
                  key={range}
                  onClick={() => setTimeRange(range as any)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    timeRange === range
                      ? "bg-white text-black"
                      : "bg-gray-800 text-gray-300 hover:bg-gray-700"
                  }`}
                >
                  {range}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="text-3xl">📝</div>
              <div className="text-green-400 text-sm font-medium">+12%</div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">{stats.totalTranslations.toLocaleString()}</div>
            <div className="text-gray-400">Total Translations</div>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="text-3xl">🔤</div>
              <div className="text-green-400 text-sm font-medium">+8%</div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">{stats.totalCharacters.toLocaleString()}</div>
            <div className="text-gray-400">Characters Translated</div>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="text-3xl">🎤</div>
              <div className="text-blue-400 text-sm font-medium">+5%</div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">{stats.totalSessions}</div>
            <div className="text-gray-400">Live Sessions</div>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="text-3xl">🎯</div>
              <div className="text-green-400 text-sm font-medium">+0.3%</div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">{stats.averageAccuracy}%</div>
            <div className="text-gray-400">Average Accuracy</div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Usage Chart */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-6">Daily Usage</h3>
            <div className="space-y-4">
              {stats.dailyUsage.map((day, index) => (
                <div key={day.date} className="flex items-center justify-between">
                  <span className="text-gray-400 text-sm">{day.date}</span>
                  <div className="flex items-center gap-4 flex-1 mx-4">
                    <div className="flex-1 bg-gray-800 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${(day.translations / 70) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-white text-sm font-medium w-8">{day.translations}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Language Distribution */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-6">Language Distribution</h3>
            <div className="space-y-6">
              {stats.topLanguages.map((lang, index) => (
                <div key={index}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white font-medium">{lang.language}</span>
                    <span className="text-gray-400">{lang.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full ${index === 0 ? 'bg-blue-500' : 'bg-green-500'}`}
                      style={{ width: `${lang.percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-sm text-gray-400 mt-1">{lang.count} translations</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Top Terms */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-6">Most Translated Terms</h3>
            <div className="space-y-4">
              {stats.topTerms.map((term, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </div>
                    <span className="text-white">{term.term}</span>
                  </div>
                  <span className="text-gray-400">{term.count} times</span>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-6">Performance Metrics</h3>
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400">Translation Speed</span>
                  <span className="text-white">1.2s avg</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "85%" }}></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400">Accuracy Rate</span>
                  <span className="text-white">99.2%</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: "99%" }}></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400">User Satisfaction</span>
                  <span className="text-white">4.8/5</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "96%" }}></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400">API Uptime</span>
                  <span className="text-white">99.9%</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "100%" }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Export Options */}
        <div className="mt-8 flex items-center justify-between bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6">
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">Export Analytics</h3>
            <p className="text-gray-400">Download detailed reports for your records</p>
          </div>
          <div className="flex items-center gap-3">
            <button className="bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
              Export CSV
            </button>
            <button className="bg-white text-black px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
              Export PDF
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
