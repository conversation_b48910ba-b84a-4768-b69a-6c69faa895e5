import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  LiveTranslationService,
  CreateSessionDto,
  JoinSessionDto,
  TranslateMessageDto,
} from './live-translation.service';
import { IsString, IsOptional, IsBoolean, IsNumber, IsIn, Length, Min, Max } from 'class-validator';

export class CreateSessionRequestDto {
  @IsString()
  @Length(1, 200)
  name: string;

  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string;

  @IsString()
  @IsIn(['ar', 'en'])
  sourceLanguage: string;

  @IsString()
  @IsIn(['ar', 'en'])
  targetLanguage: string;

  @IsOptional()
  @IsNumber()
  @Min(2)
  @Max(100)
  maxParticipants?: number;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}

export class JoinSessionRequestDto {
  @IsString()
  sessionId: string;

  @IsOptional()
  @IsString()
  @Length(0, 100)
  participantName?: string;
}

export class TranslateMessageRequestDto {
  @IsString()
  sessionId: string;

  @IsString()
  @Length(1, 1000)
  message: string;

  @IsString()
  @IsIn(['ar', 'en'])
  sourceLanguage: string;

  @IsString()
  @IsIn(['ar', 'en'])
  targetLanguage: string;
}

@ApiTags('live-translation')
@Controller('live-translation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LiveTranslationController {
  constructor(private readonly liveTranslationService: LiveTranslationService) {}

  @Post('sessions')
  @ApiOperation({ 
    summary: 'Create live translation session',
    description: 'Create a new live translation session for real-time communication'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Session created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiBody({ type: CreateSessionRequestDto })
  async createSession(
    @Request() req: any,
    @Body(ValidationPipe) createDto: CreateSessionRequestDto,
  ) {
    return await this.liveTranslationService.createSession(req.user.id, createDto);
  }

  @Post('sessions/join')
  @ApiOperation({ 
    summary: 'Join live translation session',
    description: 'Join an existing live translation session'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Joined session successfully',
  })
  @ApiResponse({ status: 400, description: 'Cannot join session' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiBody({ type: JoinSessionRequestDto })
  async joinSession(
    @Request() req: any,
    @Body(ValidationPipe) joinDto: JoinSessionRequestDto,
  ) {
    return await this.liveTranslationService.joinSession(req.user.id, joinDto);
  }

  @Post('sessions/:id/leave')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Leave live translation session',
    description: 'Leave a live translation session'
  })
  @ApiResponse({ status: 204, description: 'Left session successfully' })
  @ApiParam({ name: 'id', description: 'Session ID' })
  async leaveSession(
    @Request() req: any,
    @Param('id') sessionId: string,
  ) {
    await this.liveTranslationService.leaveSession(req.user.id, sessionId);
  }

  @Post('translate')
  @ApiOperation({ 
    summary: 'Translate message in session',
    description: 'Send and translate a message in a live session'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Message translated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        sessionId: { type: 'string' },
        userId: { type: 'string' },
        userName: { type: 'string' },
        originalMessage: { type: 'string' },
        translatedMessage: { type: 'string' },
        sourceLanguage: { type: 'string' },
        targetLanguage: { type: 'string' },
        timestamp: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid input or not in session' })
  @ApiBody({ type: TranslateMessageRequestDto })
  async translateMessage(
    @Request() req: any,
    @Body(ValidationPipe) translateDto: TranslateMessageRequestDto,
  ) {
    return await this.liveTranslationService.translateMessage(req.user.id, translateDto);
  }

  @Get('sessions')
  @ApiOperation({ 
    summary: 'Get user sessions',
    description: 'Retrieve paginated list of user sessions including public ones'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Sessions retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  async getUserSessions(
    @Request() req: any,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
  ) {
    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 20));
    
    return await this.liveTranslationService.getUserSessions(
      req.user.id,
      pageNum,
      limitNum,
    );
  }

  @Get('sessions/:id/messages')
  @ApiOperation({ 
    summary: 'Get session messages',
    description: 'Retrieve recent messages from a live session'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Messages retrieved successfully',
  })
  @ApiResponse({ status: 400, description: 'Not a participant in session' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiParam({ name: 'id', description: 'Session ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of messages to retrieve (default: 50, max: 100)' })
  async getSessionMessages(
    @Request() req: any,
    @Param('id') sessionId: string,
    @Query('limit') limit: string = '50',
  ) {
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 50));
    
    return await this.liveTranslationService.getSessionMessages(
      req.user.id,
      sessionId,
      limitNum,
    );
  }

  @Delete('sessions/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'End live translation session',
    description: 'End a live translation session (only session owner)'
  })
  @ApiResponse({ status: 204, description: 'Session ended successfully' })
  @ApiResponse({ status: 400, description: 'Only session owner can end session' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiParam({ name: 'id', description: 'Session ID' })
  async endSession(
    @Request() req: any,
    @Param('id') sessionId: string,
  ) {
    await this.liveTranslationService.endSession(req.user.id, sessionId);
  }
}
