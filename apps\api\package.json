{"name": "@tarjama/api", "version": "1.0.0", "description": "Tarjama.ai Backend API", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "npx tsx prisma/seed.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@nestjs/common": "^10.2.8", "@nestjs/core": "^10.2.8", "@nestjs/platform-express": "^10.2.8", "@nestjs/platform-socket.io": "^10.2.8", "@nestjs/websockets": "^10.2.8", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/throttler": "^5.0.1", "@nestjs/swagger": "^7.1.16", "@nestjs/schedule": "^4.0.0", "@prisma/client": "^5.6.0", "prisma": "^5.6.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "pdf-parse": "^1.1.1", "tesseract.js": "^5.0.3", "openai": "^4.20.1", "google-auth-library": "^9.4.0", "@google-cloud/vision": "^4.0.2", "@google-cloud/translate": "^8.0.2", "aws-sdk": "^2.1489.0", "redis": "^4.6.10", "socket.io": "^4.7.4", "nodemailer": "^6.9.7", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "^9.0.1", "mime-types": "^2.1.35", "cron": "^3.1.6", "joi": "^17.11.0", "tsx": "^4.6.0"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.8", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/passport-jwt": "^3.0.13", "@types/passport-google-oauth20": "^2.0.14", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/compression": "^1.7.5", "@types/joi": "^17.2.3", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "npx tsx prisma/seed.ts"}}