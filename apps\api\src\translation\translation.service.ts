import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { TranslationProvider } from '@prisma/client';

export interface TranslateTextDto {
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  glossaryId?: string;
}

export interface TranslationResult {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidenceScore?: number;
  provider: TranslationProvider;
  characterCount: number;
  processingTime: number;
}

@Injectable()
export class TranslationService {
  private readonly logger = new Logger(TranslationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async translateText(
    userId: string,
    translateDto: TranslateTextDto,
  ): Promise<TranslationResult> {
    const startTime = Date.now();
    
    try {
      // Validate input
      if (!translateDto.text || translateDto.text.trim().length === 0) {
        throw new BadRequestException('Text to translate cannot be empty');
      }

      if (translateDto.text.length > 10000) {
        throw new BadRequestException('Text too long. Maximum 10,000 characters allowed');
      }

      // Get glossary terms if specified
      let glossaryTerms = [];
      if (translateDto.glossaryId) {
        const glossary = await this.prisma.glossary.findFirst({
          where: {
            id: translateDto.glossaryId,
            OR: [
              { userId },
              { isShared: true },
            ],
          },
        });

        if (glossary) {
          glossaryTerms = Array.isArray(glossary.terms) ? glossary.terms : [];
        }
      }

      // Perform translation using mock service (replace with actual AI service)
      const translationResult = await this.performTranslation(
        translateDto.text,
        translateDto.sourceLanguage,
        translateDto.targetLanguage,
        translateDto.context,
        glossaryTerms,
      );

      const processingTime = Date.now() - startTime;

      // Save translation to database
      const savedTranslation = await this.prisma.translation.create({
        data: {
          userId,
          sourceText: translateDto.text,
          translatedText: translationResult.translatedText,
          sourceLanguage: translateDto.sourceLanguage,
          targetLanguage: translateDto.targetLanguage,
          confidenceScore: translationResult.confidenceScore,
          provider: TranslationProvider.OPENAI,
          characterCount: translateDto.text.length,
          processingTime,
          context: translateDto.context,
          glossaryId: translateDto.glossaryId,
          metadata: {
            glossaryTermsUsed: glossaryTerms.length,
            requestTimestamp: new Date().toISOString(),
          },
        },
      });

      // Update user analytics
      await this.updateUserAnalytics(userId, translateDto.text.length);

      this.logger.log(`Translation completed for user ${userId}: ${translateDto.text.length} characters`);

      return {
        translatedText: translationResult.translatedText,
        sourceLanguage: translateDto.sourceLanguage,
        targetLanguage: translateDto.targetLanguage,
        confidenceScore: translationResult.confidenceScore,
        provider: TranslationProvider.OPENAI,
        characterCount: translateDto.text.length,
        processingTime,
      };
    } catch (error) {
      this.logger.error(`Translation failed for user ${userId}:`, error);
      throw error;
    }
  }

  private async performTranslation(
    text: string,
    sourceLanguage: string,
    targetLanguage: string,
    context?: string,
    glossaryTerms: any[] = [],
  ): Promise<{ translatedText: string; confidenceScore: number }> {
    // Mock translation service - replace with actual AI service integration
    // This would integrate with OpenAI, Google Translate, DeepL, etc.
    
    const isArabicToEnglish = sourceLanguage === 'ar' && targetLanguage === 'en';
    const isEnglishToArabic = sourceLanguage === 'en' && targetLanguage === 'ar';

    let translatedText = '';
    
    if (isArabicToEnglish) {
      // Mock Arabic to English translation
      translatedText = `[EN] ${text}`;
    } else if (isEnglishToArabic) {
      // Mock English to Arabic translation
      translatedText = `[AR] ${text}`;
    } else {
      // Default translation
      translatedText = `[${targetLanguage.toUpperCase()}] ${text}`;
    }

    // Apply glossary terms
    for (const term of glossaryTerms) {
      if (term.source && term.target) {
        const regex = new RegExp(term.source, 'gi');
        translatedText = translatedText.replace(regex, term.target);
      }
    }

    // Add context if provided
    if (context) {
      translatedText = `${translatedText} [Context: ${context}]`;
    }

    return {
      translatedText,
      confidenceScore: 0.95, // Mock confidence score
    };
  }

  async getTranslationHistory(
    userId: string,
    page: number = 1,
    limit: number = 20,
  ) {
    try {
      const skip = (page - 1) * limit;

      const [translations, total] = await Promise.all([
        this.prisma.translation.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          include: {
            glossary: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        }),
        this.prisma.translation.count({
          where: { userId },
        }),
      ]);

      return {
        translations,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Error getting translation history for user ${userId}:`, error);
      throw error;
    }
  }

  async getTranslationById(userId: string, translationId: string) {
    try {
      const translation = await this.prisma.translation.findFirst({
        where: {
          id: translationId,
          userId,
        },
        include: {
          glossary: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!translation) {
        throw new BadRequestException('Translation not found');
      }

      return translation;
    } catch (error) {
      this.logger.error(`Error getting translation ${translationId} for user ${userId}:`, error);
      throw error;
    }
  }

  private async updateUserAnalytics(userId: string, charactersTranslated: number) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      await this.prisma.userAnalytics.upsert({
        where: {
          userId_date: {
            userId,
            date: today,
          },
        },
        update: {
          charactersTranslated: {
            increment: charactersTranslated,
          },
        },
        create: {
          userId,
          date: today,
          charactersTranslated,
        },
      });
    } catch (error) {
      this.logger.error(`Error updating analytics for user ${userId}:`, error);
      // Don't throw error as this is not critical
    }
  }
}
