"use client";

import { useState } from "react";
import Link from "next/link";

interface GlossaryTerm {
  id: string;
  arabicTerm: string;
  englishTerm: string;
  category: string;
  definition?: string;
  usage?: string;
  createdAt: string;
}

interface Glossary {
  id: string;
  name: string;
  description: string;
  terms: GlossaryTerm[];
  isShared: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function GlossariesPage() {
  const [glossaries] = useState<Glossary[]>([
    {
      id: "1",
      name: "Construction Materials",
      description: "Essential construction materials and their Arabic translations",
      isShared: false,
      createdAt: "2024-01-10T10:00:00Z",
      updatedAt: "2024-01-15T14:30:00Z",
      terms: [
        {
          id: "1",
          arabicTerm: "خرسانة",
          englishTerm: "Concrete",
          category: "Materials",
          definition: "A composite material composed of cement, water, and aggregates",
          usage: "Used for foundations, walls, and structural elements",
          createdAt: "2024-01-10T10:00:00Z",
        },
        {
          id: "2",
          arabicTerm: "حديد التسليح",
          englishTerm: "Reinforcement Steel",
          category: "Materials",
          definition: "Steel bars used to reinforce concrete structures",
          createdAt: "2024-01-10T10:05:00Z",
        },
      ],
    },
    {
      id: "2",
      name: "Safety Protocols",
      description: "Safety terms and procedures for construction sites",
      isShared: true,
      createdAt: "2024-01-12T09:00:00Z",
      updatedAt: "2024-01-14T16:20:00Z",
      terms: [
        {
          id: "3",
          arabicTerm: "خوذة الأمان",
          englishTerm: "Safety Helmet",
          category: "Safety Equipment",
          definition: "Protective headgear worn on construction sites",
          createdAt: "2024-01-12T09:00:00Z",
        },
        {
          id: "4",
          arabicTerm: "سترة الأمان",
          englishTerm: "Safety Vest",
          category: "Safety Equipment",
          definition: "High-visibility vest worn for safety identification",
          createdAt: "2024-01-12T09:05:00Z",
        },
      ],
    },
  ]);

  const [selectedGlossary, setSelectedGlossary] = useState<Glossary | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);

  const filteredGlossaries = glossaries.filter((glossary) =>
    glossary.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    glossary.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white">Glossaries</h1>
              <p className="text-gray-400 mt-2">Manage your construction terminology collections</p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              Create Glossary
            </button>
          </div>

          {/* Search */}
          <div className="max-w-md">
            <input
              type="text"
              placeholder="Search glossaries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/20"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Glossaries List */}
          <div className="lg:col-span-1">
            <h2 className="text-lg font-semibold text-white mb-4">Your Glossaries</h2>
            <div className="space-y-3">
              {filteredGlossaries.map((glossary) => (
                <div
                  key={glossary.id}
                  onClick={() => setSelectedGlossary(glossary)}
                  className={`p-4 rounded-xl border cursor-pointer transition-all duration-200 ${
                    selectedGlossary?.id === glossary.id
                      ? "bg-white/10 border-white/30"
                      : "bg-gray-900/50 border-gray-800 hover:bg-gray-900/70"
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-white">{glossary.name}</h3>
                    {glossary.isShared && (
                      <span className="text-xs bg-blue-900/50 text-blue-300 px-2 py-1 rounded-full">
                        Shared
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400 mb-3">{glossary.description}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{glossary.terms.length} terms</span>
                    <span>Updated {formatDate(glossary.updatedAt)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Glossary Details */}
          <div className="lg:col-span-2">
            {selectedGlossary ? (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-white">{selectedGlossary.name}</h2>
                    <p className="text-gray-400 mt-1">{selectedGlossary.description}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                      Export
                    </button>
                    <button className="bg-white text-black px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                      Add Term
                    </button>
                  </div>
                </div>

                {/* Terms List */}
                <div className="space-y-4">
                  {selectedGlossary.terms.map((term) => (
                    <div
                      key={term.id}
                      className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-sm font-medium text-gray-300 mb-2">Arabic</h4>
                          <p className="text-xl text-white font-medium mb-2" dir="rtl">
                            {term.arabicTerm}
                          </p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-300 mb-2">English</h4>
                          <p className="text-xl text-white font-medium mb-2">
                            {term.englishTerm}
                          </p>
                        </div>
                      </div>

                      {term.definition && (
                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-300 mb-2">Definition</h4>
                          <p className="text-gray-400">{term.definition}</p>
                        </div>
                      )}

                      {term.usage && (
                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-300 mb-2">Usage</h4>
                          <p className="text-gray-400">{term.usage}</p>
                        </div>
                      )}

                      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-700">
                        <div className="flex items-center gap-4 text-sm text-gray-400">
                          <span className="bg-gray-800 px-2 py-1 rounded text-xs">
                            {term.category}
                          </span>
                          <span>Added {formatDate(term.createdAt)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <button className="text-gray-400 hover:text-white transition-colors p-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button className="text-gray-400 hover:text-red-400 transition-colors p-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {selectedGlossary.terms.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">📚</div>
                    <h3 className="text-xl font-semibold text-white mb-2">No terms yet</h3>
                    <p className="text-gray-400 mb-6">Start building your glossary by adding terms</p>
                    <button className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                      Add First Term
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📖</div>
                <h3 className="text-xl font-semibold text-white mb-2">Select a glossary</h3>
                <p className="text-gray-400">Choose a glossary from the list to view and manage its terms</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
