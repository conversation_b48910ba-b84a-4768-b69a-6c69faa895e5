"use client";

import { motion } from "framer-motion";
import Link from "next/link";
// import Header from "@/components/layout/header";
// import Footer from "@/components/layout/footer";

const features = [
  {
    icon: "🌐",
    title: "Real-Time Translation",
    description:
      "Instant Arabic-English translation with 99.5% accuracy for construction terminology.",
    details: [
      "AI-powered translation engine",
      "Construction-specific terminology",
      "Real-time processing",
      "Confidence scoring",
    ],
  },
  {
    icon: "📄",
    title: "Document Translation",
    description:
      "Upload and translate construction documents, blueprints, and specifications.",
    details: [
      "PDF, Word, and image support",
      "OCR text extraction",
      "Preserve formatting",
      "Batch processing",
    ],
  },
  {
    icon: "🎤",
    title: "Live Meeting Translation",
    description:
      "Real-time speech translation for construction meetings and site communications.",
    details: [
      "Speech-to-text conversion",
      "Live translation display",
      "Meeting transcripts",
      "Multi-participant support",
    ],
  },
  {
    icon: "📚",
    title: "Personal Glossary",
    description:
      "Build and manage custom terminology databases for your projects.",
    details: [
      "Custom term management",
      "Team sharing",
      "Import/export functionality",
      "Category organization",
    ],
  },
  {
    icon: "📊",
    title: "Analytics & Insights",
    description:
      "Track usage, accuracy, and performance metrics for your translations.",
    details: [
      "Usage statistics",
      "Accuracy tracking",
      "Performance metrics",
      "Export reports",
    ],
  },
  {
    icon: "🔒",
    title: "Enterprise Security",
    description:
      "Bank-level security with data encryption and compliance standards.",
    details: [
      "End-to-end encryption",
      "GDPR compliance",
      "SOC 2 certified",
      "Private cloud options",
    ],
  },
];

const integrations = [
  { name: "Microsoft Teams", icon: "💼", status: "Available" },
  { name: "Zoom", icon: "📹", status: "Available" },
  { name: "Slack", icon: "💬", status: "Coming Soon" },
  { name: "AutoCAD", icon: "📐", status: "Coming Soon" },
  { name: "Procore", icon: "🏗️", status: "Coming Soon" },
  { name: "BIM 360", icon: "🏢", status: "Coming Soon" },
];

export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* <Header /> */}

      {/* Hero Section */}
      <section className="pt-32 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Powerful Features for
              <span className="block text-white">
                Construction Professionals
              </span>
            </h1>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
              Everything you need to break language barriers and accelerate your
              construction projects with AI-powered translation technology.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/demo"
                className="bg-white text-black px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Try Demo
              </Link>
              <Link
                href="/register"
                className="border border-gray-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-gray-900 transition-colors"
              >
                Get Started
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-8 hover:bg-gray-900/70 transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-gray-400 mb-6">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.details.map((detail, i) => (
                    <li
                      key={i}
                      className="flex items-center text-sm text-gray-300"
                    >
                      <span className="text-green-400 mr-2">✓</span>
                      {detail}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Integrations Section */}
      <section className="py-20 bg-gray-900/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Seamless Integrations
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Connect Tarjama.ai with your existing tools and workflows for
              maximum productivity.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {integrations.map((integration, index) => (
              <motion.div
                key={integration.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center hover:bg-gray-900/70 transition-all duration-300"
              >
                <div className="text-3xl mb-3">{integration.icon}</div>
                <h4 className="font-semibold text-white mb-2">
                  {integration.name}
                </h4>
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    integration.status === "Available"
                      ? "bg-green-900/50 text-green-300"
                      : "bg-yellow-900/50 text-yellow-300"
                  }`}
                >
                  {integration.status}
                </span>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Transform Your Construction Communication?
            </h2>
            <p className="text-xl text-gray-400 mb-8">
              Join thousands of construction professionals who trust Tarjama.ai
              for their translation needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/register"
                className="bg-white text-black px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Start Free Trial
              </Link>
              <Link
                href="/contact"
                className="border border-gray-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-gray-900 transition-colors"
              >
                Contact Sales
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* <Footer /> */}
    </div>
  );
}
