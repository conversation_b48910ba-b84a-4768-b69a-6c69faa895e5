import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  Body,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { FilesService, UploadedFile as FileType } from './files.service';
import { DocumentStatus } from '@prisma/client';
import { IsOptional, IsString, IsBoolean, IsIn } from 'class-validator';

export class UploadOptionsDto {
  @IsOptional()
  @IsBoolean()
  autoTranslate?: boolean;

  @IsOptional()
  @IsString()
  @IsIn(['ar', 'en'])
  sourceLanguage?: string;

  @IsOptional()
  @IsString()
  @IsIn(['ar', 'en'])
  targetLanguage?: string;

  @IsOptional()
  @IsString()
  glossaryId?: string;
}

@ApiTags('files')
@Controller('files')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ 
    summary: 'Upload document for processing',
    description: 'Upload a document (PDF, image, or Word doc) for OCR and optional translation'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Document file to upload',
        },
        autoTranslate: {
          type: 'boolean',
          description: 'Whether to automatically translate the extracted text',
        },
        sourceLanguage: {
          type: 'string',
          enum: ['ar', 'en'],
          description: 'Source language for translation',
        },
        targetLanguage: {
          type: 'string',
          enum: ['ar', 'en'],
          description: 'Target language for translation',
        },
        glossaryId: {
          type: 'string',
          description: 'Optional glossary ID to use for translation',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Document uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        filename: { type: 'string' },
        status: { type: 'string', enum: ['PROCESSING', 'COMPLETED', 'FAILED'] },
        processingTime: { type: 'number' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid file or parameters' })
  @ApiResponse({ status: 413, description: 'File too large' })
  async uploadDocument(
    @Request() req: any,
    @UploadedFile() file: FileType,
    @Body() options: UploadOptionsDto,
  ) {
    return await this.filesService.uploadDocument(req.user.id, file, options);
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get user documents',
    description: 'Retrieve paginated list of user documents with optional status filtering'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Documents retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        documents: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              originalFilename: { type: 'string' },
              filename: { type: 'string' },
              mimeType: { type: 'string' },
              fileSize: { type: 'number' },
              status: { type: 'string' },
              ocrText: { type: 'string' },
              translatedText: { type: 'string' },
              createdAt: { type: 'string', format: 'date-time' },
              translation: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  translatedText: { type: 'string' },
                  confidenceScore: { type: 'number' },
                },
              },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ 
    name: 'status', 
    required: false, 
    enum: DocumentStatus, 
    description: 'Filter by document status' 
  })
  async getDocuments(
    @Request() req: any,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('status') status?: DocumentStatus,
  ) {
    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 20));
    
    return await this.filesService.getDocuments(
      req.user.id,
      pageNum,
      limitNum,
      status,
    );
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get document by ID',
    description: 'Retrieve a specific document with its processing results'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Document retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  async getDocumentById(
    @Request() req: any,
    @Param('id') documentId: string,
  ) {
    return await this.filesService.getDocumentById(req.user.id, documentId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete document',
    description: 'Delete a document and its associated files'
  })
  @ApiResponse({ status: 204, description: 'Document deleted successfully' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  async deleteDocument(
    @Request() req: any,
    @Param('id') documentId: string,
  ) {
    await this.filesService.deleteDocument(req.user.id, documentId);
  }
}
