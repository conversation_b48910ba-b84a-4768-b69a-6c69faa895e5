"use client";

import { motion } from "framer-motion";
import <PERSON> from "next/link";
// import Header from "@/components/layout/header";
// import Footer from "@/components/layout/footer";

const team = [
  {
    name: "<PERSON>",
    role: "CEO & Co-Founder",
    bio: "Former construction project manager with 15+ years experience in GCC markets. Led $2B+ infrastructure projects.",
    image: "👨‍💼",
  },
  {
    name: "<PERSON>",
    role: "CTO & Co-Founder",
    bio: "AI/ML expert with PhD from MIT. Previously led translation teams at Google and Microsoft.",
    image: "👩‍💻",
  },
  {
    name: "<PERSON>",
    role: "Head of Product",
    bio: "Product leader with deep construction industry knowledge. Former VP at Procore.",
    image: "👨‍🔧",
  },
  {
    name: "<PERSON><PERSON>",
    role: "Head of Linguistics",
    bio: "Arabic linguistics expert with 20+ years in technical translation and localization.",
    image: "👩‍🎓",
  },
];

const values = [
  {
    icon: "🎯",
    title: "Precision",
    description:
      "We deliver accurate translations that preserve technical meaning and context.",
  },
  {
    icon: "🤝",
    title: "Partnership",
    description:
      "We work closely with our customers to understand their unique needs and challenges.",
  },
  {
    icon: "🚀",
    title: "Innovation",
    description:
      "We continuously push the boundaries of AI translation technology.",
  },
  {
    icon: "🌍",
    title: "Global Impact",
    description:
      "We're building bridges between cultures and accelerating global construction projects.",
  },
];

const stats = [
  { number: "50M+", label: "Characters Translated" },
  { number: "10K+", label: "Documents Processed" },
  { number: "500+", label: "Companies Served" },
  { number: "99.5%", label: "Translation Accuracy" },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* <Header /> */}

      {/* Hero Section */}
      <section className="pt-32 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Breaking Language Barriers in
              <span className="block text-white">Construction</span>
            </h1>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
              We're on a mission to eliminate communication barriers in the
              global construction industry through AI-powered translation
              technology designed specifically for construction professionals.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-900/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-8">Our Story</h2>
            <div className="space-y-6 text-lg text-gray-300 text-left">
              <p>
                Tarjama.ai was born from a real problem experienced by
                construction professionals working on international projects
                across the Middle East. Our founder, Ahmed Al-Rashid, was
                managing a major infrastructure project in Dubai when he
                witnessed firsthand how language barriers led to costly delays,
                safety incidents, and project overruns.
              </p>
              <p>
                Traditional translation services were too slow, too expensive,
                and lacked the technical accuracy needed for construction
                terminology. Generic translation tools failed to understand the
                nuanced language of construction, leading to dangerous
                misunderstandings on job sites.
              </p>
              <p>
                We assembled a team of construction industry veterans, AI
                researchers, and linguistics experts to build the first
                translation platform designed specifically for construction
                professionals. Today, Tarjama.ai serves thousands of
                construction companies across the GCC, helping them communicate
                more effectively and build better projects.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-900/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-4">Our Values</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              The principles that guide everything we do at Tarjama.ai.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-400">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Industry experts and technology leaders working together to
              revolutionize construction communication.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center"
              >
                <div className="text-6xl mb-4">{member.image}</div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {member.name}
                </h3>
                <p className="text-gray-400 font-medium mb-3">{member.role}</p>
                <p className="text-sm text-gray-500">{member.bio}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-gray-900/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-12"
          >
            <h2 className="text-4xl font-bold text-white mb-6">Our Mission</h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              To empower construction professionals worldwide with AI-powered
              translation technology that eliminates language barriers, improves
              safety, and accelerates project delivery. We believe that clear
              communication is the foundation of successful construction
              projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-white text-black px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Join Our Mission
              </Link>
              <Link
                href="/careers"
                className="border border-gray-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-gray-900 transition-colors"
              >
                View Careers
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* <Footer /> */}
    </div>
  );
}
