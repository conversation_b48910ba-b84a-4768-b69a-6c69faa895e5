import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { DocumentStatus } from '@prisma/client';
import * as path from 'path';
import * as crypto from 'crypto';

export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

export interface ProcessDocumentResult {
  id: string;
  filename: string;
  status: DocumentStatus;
  ocrText?: string;
  translatedText?: string;
  processingTime: number;
}

@Injectable()
export class FilesService {
  private readonly logger = new Logger(FilesService.name);
  private readonly allowedMimeTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async uploadDocument(
    userId: string,
    file: UploadedFile,
    options?: {
      autoTranslate?: boolean;
      sourceLanguage?: string;
      targetLanguage?: string;
      glossaryId?: string;
    },
  ): Promise<ProcessDocumentResult> {
    const startTime = Date.now();

    try {
      // Validate file
      this.validateFile(file);

      // Generate unique filename
      const fileExtension = path.extname(file.originalname);
      const uniqueFilename = `${crypto.randomUUID()}${fileExtension}`;
      const s3Key = `documents/${userId}/${uniqueFilename}`;

      // Mock file upload to S3 (replace with actual S3 service)
      const uploadResult = await this.uploadToS3(file.buffer, s3Key, file.mimetype);

      // Create document record
      const document = await this.prisma.document.create({
        data: {
          userId,
          originalFilename: file.originalname,
          filename: uniqueFilename,
          mimeType: file.mimetype,
          fileSize: file.size,
          s3Key,
          s3Bucket: this.configService.get('S3_BUCKET_NAME', 'tarjama-dev-uploads'),
          status: DocumentStatus.PROCESSING,
          metadata: {
            uploadedAt: new Date().toISOString(),
            autoTranslate: options?.autoTranslate || false,
            sourceLanguage: options?.sourceLanguage,
            targetLanguage: options?.targetLanguage,
            glossaryId: options?.glossaryId,
          },
        },
      });

      // Process document asynchronously
      this.processDocumentAsync(document.id, options);

      const processingTime = Date.now() - startTime;

      this.logger.log(`Document uploaded: ${document.id} for user ${userId}`);

      return {
        id: document.id,
        filename: document.originalFilename,
        status: document.status,
        processingTime,
      };
    } catch (error) {
      this.logger.error(`Error uploading document for user ${userId}:`, error);
      throw error;
    }
  }

  async getDocuments(
    userId: string,
    page: number = 1,
    limit: number = 20,
    status?: DocumentStatus,
  ) {
    try {
      const skip = (page - 1) * limit;
      const where: any = { userId };
      
      if (status) {
        where.status = status;
      }

      const [documents, total] = await Promise.all([
        this.prisma.document.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          include: {
            translation: {
              select: {
                id: true,
                translatedText: true,
                confidenceScore: true,
              },
            },
          },
        }),
        this.prisma.document.count({ where }),
      ]);

      return {
        documents,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Error getting documents for user ${userId}:`, error);
      throw error;
    }
  }

  async getDocumentById(userId: string, documentId: string) {
    try {
      const document = await this.prisma.document.findFirst({
        where: {
          id: documentId,
          userId,
        },
        include: {
          translation: true,
        },
      });

      if (!document) {
        throw new NotFoundException('Document not found');
      }

      return document;
    } catch (error) {
      this.logger.error(`Error getting document ${documentId} for user ${userId}:`, error);
      throw error;
    }
  }

  async deleteDocument(userId: string, documentId: string): Promise<void> {
    try {
      const document = await this.getDocumentById(userId, documentId);

      // Delete from S3 (mock)
      await this.deleteFromS3(document.s3Key);

      // Update document status to deleted
      await this.prisma.document.update({
        where: { id: documentId },
        data: {
          status: DocumentStatus.DELETED,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Document deleted: ${documentId} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error deleting document ${documentId} for user ${userId}:`, error);
      throw error;
    }
  }

  private validateFile(file: UploadedFile): void {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type not supported. Allowed types: ${this.allowedMimeTypes.join(', ')}`,
      );
    }

    const maxSize = this.configService.get('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
    if (file.size > maxSize) {
      throw new BadRequestException(
        `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`,
      );
    }
  }

  private async uploadToS3(buffer: Buffer, key: string, contentType: string): Promise<any> {
    // Mock S3 upload - replace with actual AWS S3 service
    this.logger.log(`Mock S3 upload: ${key}`);
    return {
      Location: `https://mock-bucket.s3.amazonaws.com/${key}`,
      Key: key,
      Bucket: 'mock-bucket',
    };
  }

  private async deleteFromS3(key: string): Promise<void> {
    // Mock S3 delete - replace with actual AWS S3 service
    this.logger.log(`Mock S3 delete: ${key}`);
  }

  private async processDocumentAsync(
    documentId: string,
    options?: {
      autoTranslate?: boolean;
      sourceLanguage?: string;
      targetLanguage?: string;
      glossaryId?: string;
    },
  ): Promise<void> {
    try {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock OCR processing
      const ocrText = 'This is mock OCR text extracted from the document. In a real implementation, this would use services like AWS Textract, Google Vision API, or Azure Computer Vision.';
      const ocrConfidence = 0.95;
      const languageDetected = options?.sourceLanguage || 'en';

      let translationId: string | undefined;
      let translatedText: string | undefined;

      // Auto-translate if requested
      if (options?.autoTranslate && options.sourceLanguage && options.targetLanguage) {
        // Mock translation
        translatedText = `[Translated from ${options.sourceLanguage} to ${options.targetLanguage}] ${ocrText}`;
        
        // Create translation record
        const translation = await this.prisma.translation.create({
          data: {
            userId: (await this.prisma.document.findUnique({ where: { id: documentId } }))!.userId,
            sourceText: ocrText,
            translatedText,
            sourceLanguage: options.sourceLanguage,
            targetLanguage: options.targetLanguage,
            confidenceScore: 0.92,
            provider: 'OPENAI',
            characterCount: ocrText.length,
            processingTime: 1500,
            context: 'Document OCR text',
            glossaryId: options.glossaryId,
          },
        });
        
        translationId = translation.id;
      }

      // Update document with processing results
      await this.prisma.document.update({
        where: { id: documentId },
        data: {
          status: DocumentStatus.COMPLETED,
          ocrText,
          ocrConfidence,
          languageDetected,
          translatedText,
          translationId,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Document processing completed: ${documentId}`);
    } catch (error) {
      this.logger.error(`Error processing document ${documentId}:`, error);
      
      // Update document status to failed
      await this.prisma.document.update({
        where: { id: documentId },
        data: {
          status: DocumentStatus.FAILED,
          updatedAt: new Date(),
        },
      });
    }
  }
}
