import { 
  FileText, 
  Mic, 
  BookOpen, 
  Users, 
  BarChart3, 
  GraduationCap,
  ArrowRight,
  Check,
  Star,
  Globe,
  Shield,
  Zap,
  Menu,
  X,
  ChevronRight,
  Play,
  Download,
  Upload,
  Settings,
  User,
  LogOut,
  Bell,
  Search,
  Filter,
  Calendar,
  Clock,
  MapPin,
  Phone,
  Mail,
  ExternalLink,
  Copy,
  Share,
  Heart,
  Bookmark,
  MessageCircle,
  ThumbsUp,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Plus,
  Minus,
  RefreshCw,
  Home,
  Building,
  Briefcase,
  Award,
  Target,
  TrendingUp,
  DollarSign,
  PieChart,
  Activity,
  Layers,
  Database,
  Server,
  Cloud,
  Lock,
  Unlock,
  Key,
  AlertTriangle,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  HelpCircle,
  type LucideIcon
} from "lucide-react"

export type IconName = 
  | "file-text"
  | "mic"
  | "book-open"
  | "users"
  | "bar-chart-3"
  | "graduation-cap"
  | "arrow-right"
  | "check"
  | "star"
  | "globe"
  | "shield"
  | "zap"
  | "menu"
  | "x"
  | "chevron-right"
  | "play"
  | "download"
  | "upload"
  | "settings"
  | "user"
  | "log-out"
  | "bell"
  | "search"
  | "filter"
  | "calendar"
  | "clock"
  | "map-pin"
  | "phone"
  | "mail"
  | "external-link"
  | "copy"
  | "share"
  | "heart"
  | "bookmark"
  | "message-circle"
  | "thumbs-up"
  | "eye"
  | "eye-off"
  | "edit"
  | "trash-2"
  | "plus"
  | "minus"
  | "refresh-cw"
  | "home"
  | "building"
  | "briefcase"
  | "award"
  | "target"
  | "trending-up"
  | "dollar-sign"
  | "pie-chart"
  | "activity"
  | "layers"
  | "database"
  | "server"
  | "cloud"
  | "lock"
  | "unlock"
  | "key"
  | "alert-triangle"
  | "alert-circle"
  | "info"
  | "check-circle"
  | "x-circle"
  | "help-circle"

const iconMap: Record<IconName, LucideIcon> = {
  "file-text": FileText,
  "mic": Mic,
  "book-open": BookOpen,
  "users": Users,
  "bar-chart-3": BarChart3,
  "graduation-cap": GraduationCap,
  "arrow-right": ArrowRight,
  "check": Check,
  "star": Star,
  "globe": Globe,
  "shield": Shield,
  "zap": Zap,
  "menu": Menu,
  "x": X,
  "chevron-right": ChevronRight,
  "play": Play,
  "download": Download,
  "upload": Upload,
  "settings": Settings,
  "user": User,
  "log-out": LogOut,
  "bell": Bell,
  "search": Search,
  "filter": Filter,
  "calendar": Calendar,
  "clock": Clock,
  "map-pin": MapPin,
  "phone": Phone,
  "mail": Mail,
  "external-link": ExternalLink,
  "copy": Copy,
  "share": Share,
  "heart": Heart,
  "bookmark": Bookmark,
  "message-circle": MessageCircle,
  "thumbs-up": ThumbsUp,
  "eye": Eye,
  "eye-off": EyeOff,
  "edit": Edit,
  "trash-2": Trash2,
  "plus": Plus,
  "minus": Minus,
  "refresh-cw": RefreshCw,
  "home": Home,
  "building": Building,
  "briefcase": Briefcase,
  "award": Award,
  "target": Target,
  "trending-up": TrendingUp,
  "dollar-sign": DollarSign,
  "pie-chart": PieChart,
  "activity": Activity,
  "layers": Layers,
  "database": Database,
  "server": Server,
  "cloud": Cloud,
  "lock": Lock,
  "unlock": Unlock,
  "key": Key,
  "alert-triangle": AlertTriangle,
  "alert-circle": AlertCircle,
  "info": Info,
  "check-circle": CheckCircle,
  "x-circle": XCircle,
  "help-circle": HelpCircle,
}

interface IconProps {
  name: IconName
  className?: string
  size?: number
}

export function Icon({ name, className, size = 20 }: IconProps) {
  const IconComponent = iconMap[name]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`)
    return null
  }

  return <IconComponent className={className} size={size} />
}

export { iconMap }
