import Link from 'next/link';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { But<PERSON> } from '@/components/ui/button';

export function CTASection() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Ready to transform your construction communication?
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Join thousands of construction professionals who trust Tarjama.ai for their translation needs.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Button size="lg" asChild>
              <Link href="/register">
                Start Free Trial
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/contact">
                Contact Sales
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
