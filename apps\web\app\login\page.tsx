"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { useAuth, useRedirectIfAuthenticated } from "@/contexts/AuthContext";
import { LoadingButton } from "@/components/ui/loading";

export default function LoginPage() {
  const { login } = useAuth();
  useRedirectIfAuthenticated();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      await login(email, password);
      // Redirect will happen automatically via useRedirectIfAuthenticated
    } catch (error) {
      setError(error instanceof Error ? error.message : "<PERSON><PERSON> failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900/20 to-black"></div>
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.02) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.02) 0%, transparent 50%)`,
          }}
        ></div>
      </div>

      <div className="relative max-w-md w-full space-y-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <Link href="/" className="inline-block group">
            <h1 className="text-4xl font-bold text-white mb-2 group-hover:text-gray-300 transition-colors">
              Tarjama.ai
            </h1>
          </Link>
          <h2 className="text-2xl font-bold text-white mb-2">Welcome back</h2>
          <p className="text-gray-400">
            Sign in to your account to continue translating
          </p>
          <div className="mt-4 p-3 bg-blue-900/20 border border-blue-800 rounded-lg">
            <p className="text-xs text-blue-300 mb-1">Demo Credentials:</p>
            <p className="text-xs text-blue-200">Email: <EMAIL></p>
            <p className="text-xs text-blue-200">Password: demo123</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mt-8 bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-8"
        >
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-5">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-300 mb-2"
                >
                  Email address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/20 focus:border-gray-600 transition-all duration-200 hover:bg-gray-800/70"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-300 mb-2"
                >
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/20 focus:border-gray-600 transition-all duration-200 hover:bg-gray-800/70"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="relative">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="sr-only"
                  />
                  <label
                    htmlFor="remember-me"
                    className="flex items-center cursor-pointer"
                  >
                    <div className="relative">
                      <div className="w-5 h-5 bg-gray-800 border-2 border-gray-600 rounded-md transition-all duration-200 hover:border-gray-500 focus-within:border-white/40 flex items-center justify-center">
                        <svg
                          className="w-3 h-3 text-white opacity-0 transition-opacity duration-200 peer-checked:opacity-100"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <input
                        type="checkbox"
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer peer"
                        onChange={(e) => {
                          const svg =
                            e.target.parentElement?.querySelector("svg");
                          const container =
                            e.target.parentElement?.querySelector("div");
                          if (svg && container) {
                            if (e.target.checked) {
                              svg.style.opacity = "1";
                              container.style.backgroundColor = "#ffffff";
                              container.style.borderColor = "#ffffff";
                              svg.style.color = "#000000";
                            } else {
                              svg.style.opacity = "0";
                              container.style.backgroundColor = "#1f2937";
                              container.style.borderColor = "#4b5563";
                              svg.style.color = "#ffffff";
                            }
                          }
                        }}
                      />
                    </div>
                    <span className="ml-3 text-sm text-gray-300">
                      Remember me
                    </span>
                  </label>
                </div>
              </div>

              <div className="text-sm">
                <Link
                  href="/forgot-password"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
            </div>

            {error && (
              <div className="bg-red-900/20 border border-red-800 text-red-400 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            <div>
              <LoadingButton
                type="submit"
                isLoading={isLoading}
                disabled={isLoading}
                className="w-full bg-white text-black py-3 px-4 rounded-xl text-base font-medium hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Sign in
              </LoadingButton>
            </div>
          </form>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-center"
        >
          <p className="text-gray-400">
            Don't have an account?{" "}
            <Link
              href="/register"
              className="text-white hover:text-gray-300 transition-colors font-medium"
            >
              Sign up
            </Link>
          </p>
        </motion.div>
      </div>
    </div>
  );
}
