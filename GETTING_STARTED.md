# 🚀 Getting Started with Tarjama.ai Development

This guide will help you set up and run the Tarjama.ai development environment on your local machine.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 20+** - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop/)
- **Git** - [Download here](https://git-scm.com/)

### Verify Installation
```bash
node --version    # Should be 20.x.x or higher
npm --version     # Should be 10.x.x or higher
docker --version  # Should be 20.x.x or higher
git --version     # Any recent version
```

## 🎯 Quick Start (Recommended)

### Option 1: Automated Setup Script

**For Linux/macOS:**
```bash
# Make the script executable
chmod +x scripts/dev-setup.sh

# Run the setup script
./scripts/dev-setup.sh
```

**For Windows:**
```cmd
# Run the Windows setup script
scripts\dev-setup.bat
```

The script will:
1. ✅ Check all prerequisites
2. 📦 Install dependencies
3. 🐳 Start Docker services (PostgreSQL, Redis, MinIO)
4. 🗄️ Set up the database with sample data
5. 🚀 Start the development servers

### Option 2: Using Makefile (Linux/macOS)

```bash
# Full setup and start
make setup

# Or step by step
make install      # Install dependencies
make docker-up    # Start Docker services
make db-setup     # Setup database
make dev          # Start development servers
```

## 🔧 Manual Setup (Step by Step)

If you prefer to set up everything manually:

### 1. Clone and Install Dependencies
```bash
# Clone the repository
git clone <your-repository-url>
cd tarjama-ai

# Install dependencies
npm install
```

### 2. Environment Configuration
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your preferred editor
# The default values should work for local development
```

### 3. Start Infrastructure Services
```bash
# Start PostgreSQL, Redis, and MinIO
docker-compose up -d postgres redis minio

# Wait for services to start (about 10-15 seconds)
```

### 4. Setup Database
```bash
# Navigate to the API directory
cd apps/api

# Generate Prisma client
npx prisma generate

# Create database schema
npx prisma db push

# Seed with sample data
npx prisma db seed

# Return to root directory
cd ../..
```

### 5. Start Development Servers
```bash
# Start both API and Web app
npm run dev
```

## 🌐 Access Your Application

Once everything is running, you can access:

| Service | URL | Description |
|---------|-----|-------------|
| **Web App** | http://localhost:3000 | Main Tarjama.ai application |
| **API** | http://localhost:3001 | Backend API server |
| **API Docs** | http://localhost:3001/api/docs | Swagger API documentation |
| **MinIO Console** | http://localhost:9001 | File storage management |

### Default Login Credentials

The database is seeded with test accounts:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | admin123 | System administrator |
| User | <EMAIL> | demo123 | Demo construction professional |
| User | <EMAIL> | demo123 | Demo project manager |
| User | <EMAIL> | demo123 | Demo team lead |

## 🛠️ Development Workflow

### Daily Development
```bash
# Start your development session
make dev
# or
npm run dev

# In separate terminals, you can:
make db-studio    # Open database browser
make logs         # View application logs
make status       # Check service status
```

### Common Commands
```bash
# Database operations
make db-reset     # Reset database completely
make db-migrate   # Run new migrations
make db-backup    # Create database backup

# Code quality
npm run lint      # Check code style
npm run test      # Run tests
npm run type-check # TypeScript checking

# Docker management
make docker-up    # Start services
make docker-down  # Stop services
make docker-logs  # View service logs
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the ports
lsof -i :3000  # Web app port
lsof -i :3001  # API port
lsof -i :5432  # PostgreSQL port

# Kill processes if needed
kill -9 <PID>
```

#### 2. Docker Services Not Starting
```bash
# Check Docker is running
docker ps

# Restart Docker services
make docker-down
make docker-up

# View detailed logs
docker-compose logs postgres
docker-compose logs redis
docker-compose logs minio
```

#### 3. Database Connection Issues
```bash
# Reset database completely
make db-reset

# Or manually recreate
docker-compose down
docker volume rm tarjama-ai_postgres_data
make docker-up
make db-setup
```

#### 4. Node Modules Issues
```bash
# Clean and reinstall
make clean
npm install
```

#### 5. Permission Issues (Linux/macOS)
```bash
# Fix script permissions
chmod +x scripts/dev-setup.sh

# Fix Docker permissions (if needed)
sudo usermod -aG docker $USER
# Then logout and login again
```

### Getting Help

If you encounter issues:

1. **Check the logs**: `make logs` or `docker-compose logs`
2. **Verify services**: `make status` or `docker-compose ps`
3. **Check health**: `make health`
4. **Reset everything**: `make clean && make setup`

## 📁 Project Structure

```
tarjama-ai/
├── apps/
│   ├── api/          # NestJS backend API
│   └── web/          # Next.js frontend
├── docs/             # Documentation
├── scripts/          # Setup and utility scripts
├── docker-compose.yml
├── package.json      # Root package.json
├── turbo.json        # Turbo build configuration
└── Makefile          # Development commands
```

## 🎯 Next Steps

Once you have the development environment running:

1. **Explore the API**: Visit http://localhost:3001/api/docs
2. **Test the Web App**: Visit http://localhost:3000
3. **Check the Database**: Run `make db-studio`
4. **Review the Code**: Start with `apps/api/src/main.ts` and `apps/web/app/page.tsx`
5. **Make Changes**: Edit files and see hot-reload in action

## 🚀 Ready to Develop!

You now have a fully functional Tarjama.ai development environment! The application includes:

- ✅ Authentication system with JWT
- ✅ Database with sample data
- ✅ File storage with MinIO
- ✅ API documentation
- ✅ Modern React frontend
- ✅ Hot reloading for development

Happy coding! 🎉
