"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  status: "processing" | "completed" | "failed";
  originalLanguage: string;
  targetLanguage: string;
  progress?: number;
  downloadUrl?: string;
  previewUrl?: string;
}

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: "1",
      name: "Construction_Safety_Manual.pdf",
      type: "application/pdf",
      size: 2048576,
      uploadedAt: "2024-01-15T10:30:00Z",
      status: "completed",
      originalLanguage: "en",
      targetLanguage: "ar",
      downloadUrl: "/downloads/safety-manual-ar.pdf",
      previewUrl: "/previews/safety-manual.jpg",
    },
    {
      id: "2",
      name: "Project_Specifications.docx",
      type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      size: 1536000,
      uploadedAt: "2024-01-15T09:15:00Z",
      status: "processing",
      originalLanguage: "ar",
      targetLanguage: "en",
      progress: 65,
    },
    {
      id: "3",
      name: "Blueprint_Annotations.png",
      type: "image/png",
      size: 3072000,
      uploadedAt: "2024-01-15T08:45:00Z",
      status: "failed",
      originalLanguage: "ar",
      targetLanguage: "en",
    },
  ]);

  const [filter, setFilter] = useState<"all" | "processing" | "completed" | "failed">("all");
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setIsUploading(true);
    
    acceptedFiles.forEach((file) => {
      const newDoc: Document = {
        id: Date.now().toString(),
        name: file.name,
        type: file.type,
        size: file.size,
        uploadedAt: new Date().toISOString(),
        status: "processing",
        originalLanguage: "auto",
        targetLanguage: "en",
        progress: 0,
      };

      setDocuments(prev => [newDoc, ...prev]);

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          clearInterval(interval);
          setDocuments(prev => 
            prev.map(doc => 
              doc.id === newDoc.id 
                ? { ...doc, status: "completed", progress: 100, downloadUrl: `/downloads/${file.name}` }
                : doc
            )
          );
          setIsUploading(false);
        } else {
          setDocuments(prev => 
            prev.map(doc => 
              doc.id === newDoc.id 
                ? { ...doc, progress }
                : doc
            )
          );
        }
      }, 500);
    });
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'image/*': ['.png', '.jpg', '.jpeg'],
      'text/plain': ['.txt'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const filteredDocuments = documents.filter(doc => 
    filter === "all" || doc.status === filter
  );

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return '📄';
    if (type.includes('word') || type.includes('document')) return '📝';
    if (type.includes('image')) return '🖼️';
    if (type.includes('text')) return '📃';
    return '📁';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-900/50 text-green-300";
      case "processing": return "bg-blue-900/50 text-blue-300";
      case "failed": return "bg-red-900/50 text-red-300";
      default: return "bg-gray-900/50 text-gray-300";
    }
  };

  const getLanguageFlag = (lang: string) => {
    if (lang === "ar") return "🇸🇦";
    if (lang === "en") return "🇺🇸";
    return "🌐";
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">Document Translation</h1>
          <p className="text-gray-400 mt-2">Upload and translate documents with OCR support</p>
        </div>

        {/* Upload Area */}
        <div className="mb-8">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-xl p-12 text-center transition-colors cursor-pointer ${
              isDragActive
                ? "border-white bg-white/5"
                : "border-gray-600 hover:border-gray-500 hover:bg-gray-900/20"
            }`}
          >
            <input {...getInputProps()} />
            <div className="text-6xl mb-4">📁</div>
            {isDragActive ? (
              <p className="text-xl text-white mb-2">Drop the files here...</p>
            ) : (
              <>
                <p className="text-xl text-white mb-2">Drag & drop files here, or click to select</p>
                <p className="text-gray-400 mb-4">
                  Supports PDF, Word, Images, and Text files (max 10MB each)
                </p>
              </>
            )}
            <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
              <span>📄 PDF</span>
              <span>📝 Word</span>
              <span>🖼️ Images</span>
              <span>📃 Text</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex gap-2">
            {["all", "processing", "completed", "failed"].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors capitalize ${
                  filter === status
                    ? "bg-white text-black"
                    : "bg-gray-800 text-gray-300 hover:bg-gray-700"
                }`}
              >
                {status}
              </button>
            ))}
          </div>
          <div className="text-sm text-gray-400">
            {filteredDocuments.length} of {documents.length} documents
          </div>
        </div>

        {/* Documents List */}
        <div className="space-y-4">
          {filteredDocuments.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📄</div>
              <h3 className="text-xl font-semibold text-white mb-2">No documents found</h3>
              <p className="text-gray-400 mb-6">
                {filter === "all" 
                  ? "Upload your first document to get started" 
                  : `No ${filter} documents at the moment`
                }
              </p>
            </div>
          ) : (
            filteredDocuments.map((doc) => (
              <div
                key={doc.id}
                className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 hover:bg-gray-900/70 transition-all duration-200"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-4 flex-1">
                    <div className="text-3xl">{getFileIcon(doc.type)}</div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-white truncate">{doc.name}</h3>
                      <div className="flex items-center gap-4 mt-1 text-sm text-gray-400">
                        <span>{formatFileSize(doc.size)}</span>
                        <span>•</span>
                        <span>{formatDate(doc.uploadedAt)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(doc.status)}`}>
                      {doc.status}
                    </span>
                    <div className="flex items-center gap-1">
                      <span className="text-lg">{getLanguageFlag(doc.originalLanguage)}</span>
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                      <span className="text-lg">{getLanguageFlag(doc.targetLanguage)}</span>
                    </div>
                  </div>
                </div>

                {/* Progress Bar for Processing */}
                {doc.status === "processing" && doc.progress !== undefined && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
                      <span>Processing document...</span>
                      <span>{Math.round(doc.progress)}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${doc.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-400">
                    {doc.status === "completed" && (
                      <span className="text-green-400">✓ Translation completed</span>
                    )}
                    {doc.status === "processing" && (
                      <span className="text-blue-400">⏳ Processing...</span>
                    )}
                    {doc.status === "failed" && (
                      <span className="text-red-400">✗ Translation failed</span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {doc.previewUrl && (
                      <button className="bg-gray-800 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm">
                        Preview
                      </button>
                    )}
                    {doc.downloadUrl && (
                      <button className="bg-white text-black px-3 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors text-sm">
                        Download
                      </button>
                    )}
                    {doc.status === "failed" && (
                      <button className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        Retry
                      </button>
                    )}
                    <button className="text-gray-400 hover:text-red-400 transition-colors p-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Usage Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">{documents.length}</div>
            <div className="text-gray-400">Total Documents</div>
          </div>
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">
              {documents.filter(d => d.status === "completed").length}
            </div>
            <div className="text-gray-400">Completed</div>
          </div>
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">
              {documents.filter(d => d.status === "processing").length}
            </div>
            <div className="text-gray-400">Processing</div>
          </div>
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">
              {Math.round(documents.reduce((acc, doc) => acc + doc.size, 0) / 1024 / 1024)}MB
            </div>
            <div className="text-gray-400">Total Size</div>
          </div>
        </div>
      </div>
    </div>
  );
}
