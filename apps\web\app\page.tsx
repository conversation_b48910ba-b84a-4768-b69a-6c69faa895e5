import { HeroSection } from "@/components/sections/hero-section";
import { FeaturesSection } from "@/components/sections/features-section";
import { TestimonialsSection } from "@/components/sections/testimonials-section";
import { PricingSection } from "@/components/sections/pricing-section";
import { CTASection } from "@/components/sections/cta-section";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";

export default function HomePage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <Header />
      <main>
        <HeroSection />
        <FeaturesSection />
        <TestimonialsSection />
        <PricingSection />
        <CTASection />
      </main>
      <Footer />
    </div>
  );
}

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                Features
              </a>
              <a
                href="#pricing"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                Pricing
              </a>
              <a
                href="#about"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                About
              </a>
              <a
                href="/login"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                Login
              </a>
              <a
                href="/register"
                className="btn-primary text-sm px-6 py-3 rounded-lg"
              >
                Get Started
              </a>
            </nav>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button className="text-white">☰</button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        {/* Hero Section */}
        <section className="relative py-24 lg:py-40 overflow-hidden">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900/20 via-gray-800/10 to-black" />

          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]" />

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="mb-8">
                <span className="inline-block mb-6 px-4 py-2 text-sm bg-gray-800 text-gray-300 rounded-full">
                  ⚡ AI-Powered Translation Platform
                </span>
              </div>

              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-[1.1] mb-8 tracking-tight">
                Professional{" "}
                <span className="gradient-text">
                  Arabic-English Translation
                </span>{" "}
                for Construction Teams
              </h1>

              <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-12 leading-relaxed font-medium">
                Streamline your construction projects with real-time
                translation, document processing, and specialized terminology
                management. Built specifically for the construction industry.
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4 mb-16">
                <a
                  href="/register"
                  className="btn-primary text-lg px-8 py-4 rounded-xl inline-flex items-center justify-center"
                >
                  ⚡ Start Free Trial
                </a>
                <a
                  href="/demo"
                  className="btn-secondary text-lg px-8 py-4 rounded-xl inline-flex items-center justify-center"
                >
                  ▶ Watch Demo
                </a>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
                <div className="text-center p-6 bg-gray-900/50 rounded-xl border border-gray-800">
                  <div className="w-12 h-12 mx-auto mb-4 bg-gray-700/30 rounded-lg flex items-center justify-center">
                    🎯
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    99.5%
                  </div>
                  <div className="text-gray-400 text-sm font-medium">
                    Translation Accuracy
                  </div>
                </div>
                <div className="text-center p-6 bg-gray-900/50 rounded-xl border border-gray-800">
                  <div className="w-12 h-12 mx-auto mb-4 bg-gray-700/30 rounded-lg flex items-center justify-center">
                    🏢
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    50+
                  </div>
                  <div className="text-gray-400 text-sm font-medium">
                    Construction Companies
                  </div>
                </div>
                <div className="text-center p-6 bg-gray-900/50 rounded-xl border border-gray-800">
                  <div className="w-12 h-12 mx-auto mb-4 bg-gray-700/30 rounded-lg flex items-center justify-center">
                    📄
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    1M+
                  </div>
                  <div className="text-gray-400 text-sm font-medium">
                    Documents Processed
                  </div>
                </div>
                <div className="text-center p-6 bg-gray-900/50 rounded-xl border border-gray-800">
                  <div className="w-12 h-12 mx-auto mb-4 bg-gray-700/30 rounded-lg flex items-center justify-center">
                    🛡️
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    24/7
                  </div>
                  <div className="text-gray-400 text-sm font-medium">
                    Live Support
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-24 bg-gray-950/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-20">
              <div className="mb-6">
                <span className="inline-block px-4 py-2 bg-gray-800 text-gray-300 rounded-full text-sm">
                  ⭐ Premium Features
                </span>
              </div>
              <h2 className="text-3xl md:text-5xl font-bold mb-6 tracking-tight">
                Everything you need for{" "}
                <span className="gradient-text">construction translation</span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto font-medium">
                Purpose-built features for construction professionals working
                across Arabic and English languages. Streamline your workflow
                with AI-powered tools designed for the industry.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="p-8 bg-gray-900/50 rounded-xl border border-gray-800 hover:scale-105 transition-all duration-300 group cursor-pointer">
                <div className="w-14 h-14 bg-gray-700/30 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  📄
                </div>
                <h3 className="text-xl font-bold mb-3">Document Translation</h3>
                <p className="text-gray-400 leading-relaxed font-medium">
                  Upload PDFs, images, and Word documents for instant AI-powered
                  translation with construction-specific terminology.
                </p>
              </div>

              <div className="p-8 bg-gray-900/50 rounded-xl border border-gray-800 hover:scale-105 transition-all duration-300 group cursor-pointer">
                <div className="w-14 h-14 bg-gray-700/30 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  🎤
                </div>
                <h3 className="text-xl font-bold mb-3">Live Translation</h3>
                <p className="text-gray-400 leading-relaxed font-medium">
                  Real-time voice and text translation for meetings, site
                  visits, and team collaboration across language barriers.
                </p>
              </div>

              <div className="p-8 bg-gray-900/50 rounded-xl border border-gray-800 hover:scale-105 transition-all duration-300 group cursor-pointer">
                <div className="w-14 h-14 bg-gray-700/30 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  📚
                </div>
                <h3 className="text-xl font-bold mb-3">Smart Glossaries</h3>
                <p className="text-gray-400 leading-relaxed font-medium">
                  Build and manage custom glossaries with construction
                  terminology for consistent, accurate translations across
                  projects.
                </p>
              </div>

              <div className="p-8 bg-gray-900/50 rounded-xl border border-gray-800 hover:scale-105 transition-all duration-300 group cursor-pointer">
                <div className="w-14 h-14 bg-gray-700/30 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  👥
                </div>
                <h3 className="text-xl font-bold mb-3">Team Collaboration</h3>
                <p className="text-gray-400 leading-relaxed font-medium">
                  Share translations, collaborate on projects, and maintain
                  consistency across your entire construction team.
                </p>
              </div>

              <div className="p-8 bg-gray-900/50 rounded-xl border border-gray-800 hover:scale-105 transition-all duration-300 group cursor-pointer">
                <div className="w-14 h-14 bg-gray-700/30 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  📊
                </div>
                <h3 className="text-xl font-bold mb-3">Analytics & Insights</h3>
                <p className="text-gray-400 leading-relaxed font-medium">
                  Track translation usage, quality metrics, and team
                  productivity with comprehensive analytics dashboard.
                </p>
              </div>

              <div className="p-8 bg-gray-900/50 rounded-xl border border-gray-800 hover:scale-105 transition-all duration-300 group cursor-pointer">
                <div className="w-14 h-14 bg-gray-700/30 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  🎓
                </div>
                <h3 className="text-xl font-bold mb-3">Language Learning</h3>
                <p className="text-gray-400 leading-relaxed font-medium">
                  Interactive flashcards and learning modules to improve your
                  Arabic-English construction vocabulary.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
