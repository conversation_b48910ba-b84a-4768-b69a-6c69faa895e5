"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Icon } from "@/components/ui/icon";
import { cn } from "@/lib/utils";

export default function HomePage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="border-b border-gray-800 bg-black/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            {/* Logo */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="text-2xl font-bold gradient-text"
            >
              Tarjama.ai
            </motion.div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                Features
              </a>
              <a
                href="#pricing"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                Pricing
              </a>
              <a
                href="#about"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                About
              </a>
              <Link
                href="/login"
                className="text-gray-300 hover:text-white transition-all duration-200 font-medium"
              >
                Login
              </Link>
              <Button asChild variant="default" size="default">
                <Link href="/register">
                  Get Started
                  <Icon name="arrow-right" className="ml-2" size={16} />
                </Link>
              </Button>
            </nav>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button variant="ghost" size="icon">
                <Icon name="menu" size={20} />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        {/* Hero Section */}
        <section className="relative py-24 lg:py-40 overflow-hidden">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-purple-600/5 to-black" />

          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]" />

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="mb-8"
              >
                <Badge variant="secondary" className="mb-6 px-4 py-2 text-sm">
                  <Icon name="zap" className="mr-2" size={14} />
                  AI-Powered Translation Platform
                </Badge>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                className="text-4xl md:text-6xl lg:text-7xl font-bold leading-[1.1] mb-8 tracking-tight"
              >
                Professional{" "}
                <span className="gradient-text">
                  Arabic-English Translation
                </span>{" "}
                for Construction Teams
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-xl text-gray-400 max-w-3xl mx-auto mb-12 leading-relaxed font-medium"
              >
                Streamline your construction projects with real-time
                translation, document processing, and specialized terminology
                management. Built specifically for the construction industry.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="flex flex-col sm:flex-row justify-center gap-4 mb-16"
              >
                <Button asChild size="xl" variant="default">
                  <Link href="/register">
                    <Icon name="zap" className="mr-2" size={18} />
                    Start Free Trial
                  </Link>
                </Button>
                <Button asChild size="xl" variant="outline">
                  <Link href="/demo">
                    <Icon name="play" className="mr-2" size={18} />
                    Watch Demo
                  </Link>
                </Button>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto"
              >
                {[
                  {
                    value: "99.5%",
                    label: "Translation Accuracy",
                    icon: "target",
                  },
                  {
                    value: "50+",
                    label: "Construction Companies",
                    icon: "building",
                  },
                  {
                    value: "1M+",
                    label: "Documents Processed",
                    icon: "file-text",
                  },
                  { value: "24/7", label: "Live Support", icon: "shield" },
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                  >
                    <Card className="text-center p-6 hover:scale-105 transition-transform duration-200">
                      <CardContent className="p-0">
                        <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                          <Icon
                            name={stat.icon as any}
                            size={24}
                            className="text-white"
                          />
                        </div>
                        <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                          {stat.value}
                        </div>
                        <div className="text-gray-400 text-sm font-medium">
                          {stat.label}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-24 bg-gray-950/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-20">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="mb-6"
              >
                <Badge variant="outline" className="px-4 py-2">
                  <Icon name="star" className="mr-2" size={14} />
                  Premium Features
                </Badge>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-3xl md:text-5xl font-bold mb-6 tracking-tight"
              >
                Everything you need for{" "}
                <span className="gradient-text">construction translation</span>
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-xl text-gray-400 max-w-3xl mx-auto font-medium"
              >
                Purpose-built features for construction professionals working
                across Arabic and English languages. Streamline your workflow
                with AI-powered tools designed for the industry.
              </motion.p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="p-8 h-full hover:scale-105 transition-all duration-300 group cursor-pointer">
                    <CardHeader className="p-0 mb-6">
                      <div className="w-14 h-14 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <Icon
                          name={feature.iconName}
                          size={28}
                          className="text-white"
                        />
                      </div>
                      <CardTitle className="text-xl font-bold mb-3">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <p className="text-gray-400 leading-relaxed font-medium">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}

const features = [
  {
    icon: "📄",
    iconName: "file-text",
    title: "Document Translation",
    description:
      "Upload PDFs, images, and Word documents for instant AI-powered translation with construction-specific terminology.",
  },
  {
    icon: "🎤",
    iconName: "mic",
    title: "Live Translation",
    description:
      "Real-time voice and text translation for meetings, site visits, and team collaboration across language barriers.",
  },
  {
    icon: "📚",
    iconName: "book-open",
    title: "Smart Glossaries",
    description:
      "Build and manage custom glossaries with construction terminology for consistent, accurate translations across projects.",
  },
  {
    icon: "👥",
    iconName: "users",
    title: "Team Collaboration",
    description:
      "Share translations, collaborate on projects, and maintain consistency across your entire construction team.",
  },
  {
    icon: "📊",
    iconName: "bar-chart-3",
    title: "Analytics & Insights",
    description:
      "Track translation usage, quality metrics, and team productivity with comprehensive analytics dashboard.",
  },
  {
    icon: "🎓",
    iconName: "graduation-cap",
    title: "Language Learning",
    description:
      "Interactive flashcards and learning modules to improve your Arabic-English construction vocabulary.",
  },
] as const;
