import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRightIcon, CheckIcon, StarIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { HeroSection } from '@/components/sections/hero-section';
import { FeaturesSection } from '@/components/sections/features-section';
import { PricingSection } from '@/components/sections/pricing-section';
import { TestimonialsSection } from '@/components/sections/testimonials-section';
import { CTASection } from '@/components/sections/cta-section';

export const metadata: Metadata = {
  title: 'AI-Powered Arabic-English Translation for Construction Professionals',
  description: 'Transform your construction projects with Tarjama.ai - the first specialized Arabic-English translation platform designed for construction professionals. Real-time translation, document processing, and team collaboration.',
  openGraph: {
    title: 'Tarjama.ai - AI-Powered Arabic-English Translation for Construction',
    description: 'Transform your construction projects with specialized Arabic-English translation. Real-time translation, document processing, and team collaboration.',
    images: ['/og-home.png'],
  },
};

export default function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <HeroSection />
        
        {/* Features Section */}
        <FeaturesSection />
        
        {/* Stats Section */}
        <section className="py-16 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">90%</div>
                <div className="text-sm text-muted-foreground">Cost Savings</div>
              </div>
              <div>
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">10x</div>
                <div className="text-sm text-muted-foreground">Faster Translation</div>
              </div>
              <div>
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">95%</div>
                <div className="text-sm text-muted-foreground">Accuracy Rate</div>
              </div>
              <div>
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">24/7</div>
                <div className="text-sm text-muted-foreground">Availability</div>
              </div>
            </div>
          </div>
        </section>
        
        {/* How It Works Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                How Tarjama.ai Works
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Get started with professional Arabic-English translation in three simple steps
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-primary">1</span>
                  </div>
                  <CardTitle>Upload Your Document</CardTitle>
                  <CardDescription>
                    Drag and drop your PDF, image, or document. Our AI instantly detects the language and content type.
                  </CardDescription>
                </CardHeader>
              </Card>
              
              <Card className="text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-primary">2</span>
                  </div>
                  <CardTitle>AI-Powered Translation</CardTitle>
                  <CardDescription>
                    Our construction-specialized AI translates your content with industry-specific terminology and context.
                  </CardDescription>
                </CardHeader>
              </Card>
              
              <Card className="text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-primary">3</span>
                  </div>
                  <CardTitle>Review & Export</CardTitle>
                  <CardDescription>
                    Review the translation, make edits if needed, and export in your preferred format. Share with your team instantly.
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>
          </div>
        </section>
        
        {/* Testimonials Section */}
        <TestimonialsSection />
        
        {/* Pricing Section */}
        <PricingSection />
        
        {/* Integration Section */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Integrates with Your Workflow
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Seamlessly connect with the tools you already use
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center justify-center">
              {[
                'Microsoft Teams',
                'Zoom',
                'Slack',
                'Procore',
                'Autodesk',
                'Google Drive',
              ].map((integration) => (
                <div key={integration} className="text-center">
                  <div className="w-16 h-16 bg-white rounded-lg shadow-sm flex items-center justify-center mx-auto mb-2">
                    <div className="w-8 h-8 bg-muted rounded"></div>
                  </div>
                  <div className="text-sm text-muted-foreground">{integration}</div>
                </div>
              ))}
            </div>
          </div>
        </section>
        
        {/* CTA Section */}
        <CTASection />
      </main>
      
      <Footer />
    </div>
  );
}
