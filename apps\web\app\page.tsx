import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "AI-Powered Arabic-English Translation for Construction Professionals",
  description:
    "Transform your construction projects with Tarjama.ai - the first specialized Arabic-English translation platform designed for construction professionals. Real-time translation, document processing, and team collaboration.",
  openGraph: {
    title:
      "Tarjama.ai - AI-Powered Arabic-English Translation for Construction",
    description:
      "Transform your construction projects with specialized Arabic-English translation. Real-time translation, document processing, and team collaboration.",
    images: ["/og-home.png"],
  },
};

export default function HomePage() {
  return (
    <div
      style={{
        backgroundColor: "#0f172a",
        color: "#ffffff",
        minHeight: "100vh",
        fontFamily: "system-ui, -apple-system, sans-serif",
      }}
    >
      {/* Header */}
      <header
        style={{
          position: "sticky",
          top: 0,
          zIndex: 50,
          width: "100%",
          borderBottom: "1px solid #374151",
          backgroundColor: "rgba(15, 23, 42, 0.95)",
          backdropFilter: "blur(8px)",
        }}
      >
        <div
          style={{
            maxWidth: "1200px",
            margin: "0 auto",
            display: "flex",
            height: "64px",
            alignItems: "center",
            padding: "0 16px",
          }}
        >
          <div style={{ marginRight: "16px", display: "flex" }}>
            <Link
              href="/"
              style={{
                marginRight: "24px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                textDecoration: "none",
              }}
            >
              <div
                style={{
                  height: "32px",
                  width: "32px",
                  borderRadius: "8px",
                  background: "linear-gradient(to right, #3b82f6, #8b5cf6)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <span
                  style={{
                    color: "white",
                    fontWeight: "bold",
                    fontSize: "14px",
                  }}
                >
                  T
                </span>
              </div>
              <span
                style={{
                  fontWeight: "bold",
                  fontSize: "20px",
                  background: "linear-gradient(to right, #60a5fa, #a78bfa)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Tarjama.ai
              </span>
            </Link>
          </div>
          <div
            style={{
              display: "flex",
              flex: 1,
              alignItems: "center",
              justifyContent: "flex-end",
              gap: "16px",
            }}
          >
            <Link
              href="/login"
              style={{
                fontSize: "14px",
                fontWeight: "500",
                color: "#d1d5db",
                textDecoration: "none",
                transition: "color 0.2s",
              }}
              onMouseOver={(e) => (e.target.style.color = "#ffffff")}
              onMouseOut={(e) => (e.target.style.color = "#d1d5db")}
            >
              Login
            </Link>
            <Link
              href="/register"
              style={{
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "8px",
                background: "linear-gradient(to right, #2563eb, #7c3aed)",
                padding: "8px 16px",
                fontSize: "14px",
                fontWeight: "500",
                color: "white",
                textDecoration: "none",
                transition: "all 0.2s",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
              }}
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ flex: 1 }}>
        {/* Hero Section */}
        <section
          style={{
            position: "relative",
            padding: "80px 0 128px",
            overflow: "hidden",
            background:
              "linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)",
          }}
        >
          <div
            style={{
              maxWidth: "1200px",
              margin: "0 auto",
              padding: "0 16px",
              position: "relative",
            }}
          >
            <div
              style={{
                maxWidth: "896px",
                margin: "0 auto",
                textAlign: "center",
              }}
            >
              <div style={{ marginBottom: "32px" }}>
                <span
                  style={{
                    display: "inline-flex",
                    alignItems: "center",
                    borderRadius: "9999px",
                    backgroundColor: "rgba(59, 130, 246, 0.1)",
                    padding: "4px 12px",
                    fontSize: "14px",
                    fontWeight: "500",
                    color: "#60a5fa",
                    border: "1px solid rgba(59, 130, 246, 0.2)",
                  }}
                >
                  🚀 Now in Beta - Free for Early Users
                </span>
              </div>
              <h1
                style={{
                  fontSize: "48px",
                  fontWeight: "bold",
                  lineHeight: "1.1",
                  color: "white",
                  marginBottom: "24px",
                }}
              >
                AI-Powered{" "}
                <span
                  style={{
                    background:
                      "linear-gradient(to right, #60a5fa, #a78bfa, #60a5fa)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    animation: "pulse 2s infinite",
                  }}
                >
                  Arabic-English Translation
                </span>{" "}
                for Construction
              </h1>
              <p
                style={{
                  marginTop: "24px",
                  fontSize: "18px",
                  lineHeight: "1.6",
                  color: "#d1d5db",
                  maxWidth: "512px",
                  margin: "24px auto 0",
                }}
              >
                Transform your construction projects with specialized
                translation technology. Real-time translation, document
                processing, and team collaboration designed specifically for
                construction professionals.
              </p>
              <div
                style={{
                  marginTop: "40px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: "24px",
                  flexWrap: "wrap",
                }}
              >
                <Link
                  href="/register"
                  style={{
                    display: "inline-flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: "8px",
                    background: "linear-gradient(to right, #2563eb, #7c3aed)",
                    padding: "16px 32px",
                    fontSize: "14px",
                    fontWeight: "500",
                    color: "white",
                    textDecoration: "none",
                    transition: "all 0.2s",
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.25)",
                  }}
                >
                  Get Started Free
                  <svg
                    style={{
                      marginLeft: "8px",
                      height: "16px",
                      width: "16px",
                    }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Link>
                <Link
                  href="/demo"
                  style={{
                    display: "inline-flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: "8px",
                    border: "1px solid #4b5563",
                    backgroundColor: "rgba(31, 41, 55, 0.5)",
                    padding: "16px 32px",
                    fontSize: "14px",
                    fontWeight: "500",
                    color: "#d1d5db",
                    textDecoration: "none",
                    transition: "all 0.2s",
                    backdropFilter: "blur(8px)",
                  }}
                >
                  <svg
                    style={{
                      marginRight: "8px",
                      height: "16px",
                      width: "16px",
                    }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Watch Demo
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section
          style={{
            padding: "80px 0",
            backgroundColor: "rgba(31, 41, 55, 0.5)",
          }}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-white">
                Everything you need for construction translation
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-300">
                Purpose-built features for construction professionals working
                across Arabic and English languages.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
              <div className="group flex flex-col p-8 bg-gray-900/50 rounded-xl shadow-xl border border-gray-700/50 hover:border-blue-500/50 transition-all hover:shadow-2xl backdrop-blur">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center mr-4">
                    <svg
                      className="h-6 w-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-white group-hover:text-blue-400 transition-colors">
                    Real-time Translation
                  </h3>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  Instant Arabic-English translation during live meetings and
                  conversations with industry-specific accuracy.
                </p>
              </div>
              <div className="group flex flex-col p-8 bg-gray-900/50 rounded-xl shadow-xl border border-gray-700/50 hover:border-purple-500/50 transition-all hover:shadow-2xl backdrop-blur">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center mr-4">
                    <svg
                      className="h-6 w-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-white group-hover:text-purple-400 transition-colors">
                    Document Processing
                  </h3>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  Upload PDFs, images, and documents for accurate translation
                  with construction context preservation.
                </p>
              </div>
              <div className="group flex flex-col p-8 bg-gray-900/50 rounded-xl shadow-xl border border-gray-700/50 hover:border-green-500/50 transition-all hover:shadow-2xl backdrop-blur">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center mr-4">
                    <svg
                      className="h-6 w-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-white group-hover:text-green-400 transition-colors">
                    Construction Terminology
                  </h3>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  Specialized AI trained on construction industry terminology
                  and technical specifications.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="group">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform">
                  90%
                </div>
                <div className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                  Cost Savings
                </div>
              </div>
              <div className="group">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform">
                  10x
                </div>
                <div className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                  Faster Translation
                </div>
              </div>
              <div className="group">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform">
                  95%
                </div>
                <div className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                  Accuracy Rate
                </div>
              </div>
              <div className="group">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform">
                  24/7
                </div>
                <div className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                  Availability
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-white/10 via-transparent to-transparent"></div>

          <div className="container mx-auto px-4 relative">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-white">
                Ready to transform your construction communication?
              </h2>
              <p className="mt-6 text-lg leading-8 text-blue-100">
                Join thousands of construction professionals who trust
                Tarjama.ai for their translation needs.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Link
                  href="/register"
                  className="group inline-flex items-center justify-center rounded-lg bg-white px-8 py-4 text-sm font-medium text-blue-600 hover:bg-gray-50 transition-all shadow-xl hover:shadow-2xl hover:scale-105"
                >
                  Start Free Trial
                  <svg
                    className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center rounded-lg border border-white/30 bg-white/10 px-8 py-4 text-sm font-medium text-white hover:bg-white/20 transition-all backdrop-blur"
                >
                  <svg
                    className="mr-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                  Contact Sales
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t border-gray-800 bg-gray-900">
        <div className="container mx-auto py-12 md:py-16 px-4">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white font-bold text-sm">T</span>
                </div>
                <h3 className="text-lg font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Tarjama.ai
                </h3>
              </div>
              <p className="text-sm text-gray-400 leading-relaxed">
                AI-powered Arabic-English translation for construction
                professionals. Building bridges through language.
              </p>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-white mb-4">Product</h4>
              <ul className="space-y-3 text-sm text-gray-400">
                <li>
                  <Link
                    href="/features"
                    className="hover:text-white transition-colors"
                  >
                    Features
                  </Link>
                </li>
                <li>
                  <Link
                    href="/pricing"
                    className="hover:text-white transition-colors"
                  >
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link
                    href="/docs"
                    className="hover:text-white transition-colors"
                  >
                    Documentation
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-3 text-sm text-gray-400">
                <li>
                  <Link
                    href="/about"
                    className="hover:text-white transition-colors"
                  >
                    About
                  </Link>
                </li>
                <li>
                  <Link
                    href="/contact"
                    className="hover:text-white transition-colors"
                  >
                    Contact
                  </Link>
                </li>
                <li>
                  <Link
                    href="/privacy"
                    className="hover:text-white transition-colors"
                  >
                    Privacy
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-white mb-4">Support</h4>
              <ul className="space-y-3 text-sm text-gray-400">
                <li>
                  <Link
                    href="/help"
                    className="hover:text-white transition-colors"
                  >
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link
                    href="/status"
                    className="hover:text-white transition-colors"
                  >
                    Status
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-400">
              &copy; 2024 Tarjama.ai. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link
                href="/terms"
                className="text-sm text-gray-400 hover:text-white transition-colors"
              >
                Terms
              </Link>
              <Link
                href="/privacy"
                className="text-sm text-gray-400 hover:text-white transition-colors"
              >
                Privacy
              </Link>
              <Link
                href="/cookies"
                className="text-sm text-gray-400 hover:text-white transition-colors"
              >
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
