import { useState, useEffect, useCallback } from "react";
import { apiClient } from "@/lib/api";

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

export function useApi<T>(
  apiCall: () => Promise<T>,
  options: UseApiOptions = {}
) {
  const { immediate = true, onSuccess, onError } = options;

  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: immediate,
    error: null,
  });

  const execute = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const data = await apiCall();
      setState({ data, loading: false, error: null });
      onSuccess?.(data);
      return data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      setState({ data: null, loading: false, error: errorMessage });
      onError?.(errorMessage);
      throw error;
    }
  }, [apiCall, onSuccess, onError]);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  return {
    ...state,
    execute,
    reset,
    refetch: execute,
  };
}

// Specific hooks for common API calls
export function useTranslations(params?: {
  page?: number;
  limit?: number;
  type?: string;
  status?: string;
}) {
  return useApi(() =>
    apiClient.getTranslationHistory(params?.page, params?.limit)
  );
}

export function useGlossaries() {
  return useApi(() => apiClient.getGlossaries());
}

export function useDocuments(params?: {
  page?: number;
  limit?: number;
  status?: string;
}) {
  return useApi(() => apiClient.getDocuments(params));
}

export function useAnalytics(params?: {
  timeRange?: string;
  metrics?: string[];
}) {
  return useApi(() => apiClient.getAnalytics(params));
}

export function useProfile() {
  return useApi(() => apiClient.getProfile());
}

// Mutation hooks for actions
export function useTranslate() {
  const [state, setState] = useState<UseApiState<any>>({
    data: null,
    loading: false,
    error: null,
  });

  const translate = useCallback(
    async (data: {
      text: string;
      sourceLanguage: string;
      targetLanguage: string;
      context?: string;
    }) => {
      setState({ data: null, loading: true, error: null });

      try {
        const result = await apiClient.translate(data);
        setState({ data: result, loading: false, error: null });
        return result;
      } catch (error) {
        console.warn("API translation failed, using fallback:", error);

        // Fallback translation for demo purposes
        const fallbackTranslations: Record<string, string> = {
          "مشروع البناء يتطلب مواد عالية الجودة":
            "Construction project requires high-quality materials",
          خرسانة: "Concrete",
          "حديد التسليح": "Reinforcement steel",
          "مواد البناء": "Building materials",
          السلامة: "Safety",
          مقاول: "Contractor",
          "Construction project requires high-quality materials":
            "مشروع البناء يتطلب مواد عالية الجودة",
          Concrete: "خرسانة",
          "Reinforcement steel": "حديد التسليح",
          "Building materials": "مواد البناء",
          Safety: "السلامة",
          Contractor: "مقاول",
        };

        const fallbackResult = {
          translatedText:
            fallbackTranslations[data.text] ||
            (data.sourceLanguage === "ar"
              ? "High-quality construction translation"
              : "ترجمة بناء عالية الجودة"),
          confidence: 0.95,
          alternatives: [],
        };

        setState({ data: fallbackResult, loading: false, error: null });
        return fallbackResult;
      }
    },
    []
  );

  return {
    ...state,
    translate,
  };
}

export function useUploadDocument() {
  const [state, setState] = useState<UseApiState<any>>({
    data: null,
    loading: false,
    error: null,
  });

  const upload = useCallback(
    async (
      file: File,
      options: {
        sourceLanguage: string;
        targetLanguage: string;
        ocrEnabled?: boolean;
      }
    ) => {
      setState({ data: null, loading: true, error: null });

      try {
        const result = await apiClient.uploadDocument(file, options);
        setState({ data: result, loading: false, error: null });
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Upload failed";
        setState({ data: null, loading: false, error: errorMessage });
        throw error;
      }
    },
    []
  );

  return {
    ...state,
    upload,
  };
}

export function useAuth() {
  const [state, setState] = useState<UseApiState<any>>({
    data: null,
    loading: false,
    error: null,
  });

  const login = useCallback(async (email: string, password: string) => {
    setState({ data: null, loading: true, error: null });

    try {
      const result = await apiClient.login({ email, password });
      setState({ data: result, loading: false, error: null });
      return result;
    } catch (error) {
      console.warn("API login failed, using demo mode:", error);

      // Demo credentials for testing
      if (email === "<EMAIL>" && password === "demo123") {
        const demoResult = {
          user: {
            id: "demo-user",
            email: "<EMAIL>",
            name: "Demo User",
            company: "Demo Construction Co.",
            planType: "Professional",
          },
          tokens: {
            accessToken: "demo-token-123",
          },
        };
        setState({ data: demoResult, loading: false, error: null });
        return demoResult;
      }

      const errorMessage =
        error instanceof Error ? error.message : "Login failed";
      setState({ data: null, loading: false, error: errorMessage });
      throw error;
    }
  }, []);

  const register = useCallback(
    async (data: {
      email: string;
      password: string;
      name: string;
      firstName?: string;
      lastName?: string;
      company?: string;
      position?: string;
    }) => {
      setState({ data: null, loading: true, error: null });

      try {
        const result = await apiClient.register(data);
        setState({ data: result, loading: false, error: null });
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Registration failed";
        setState({ data: null, loading: false, error: errorMessage });
        throw error;
      }
    },
    []
  );

  const logout = useCallback(async () => {
    setState({ data: null, loading: true, error: null });

    try {
      await apiClient.logout();
      setState({ data: null, loading: false, error: null });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Logout failed";
      setState({ data: null, loading: false, error: errorMessage });
      throw error;
    }
  }, []);

  return {
    ...state,
    login,
    register,
    logout,
  };
}

// Generic mutation hook
export function useMutation<T, P>(
  mutationFn: (params: P) => Promise<T>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: string) => void;
  } = {}
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const mutate = useCallback(
    async (params: P) => {
      setState({ data: null, loading: true, error: null });

      try {
        const result = await mutationFn(params);
        setState({ data: result, loading: false, error: null });
        options.onSuccess?.(result);
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Mutation failed";
        setState({ data: null, loading: false, error: errorMessage });
        options.onError?.(errorMessage);
        throw error;
      }
    },
    [mutationFn, options]
  );

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  return {
    ...state,
    mutate,
    reset,
  };
}

// Error boundary hook
export function useErrorHandler() {
  const [error, setError] = useState<string | null>(null);

  const handleError = useCallback((error: any) => {
    const message =
      error instanceof Error ? error.message : "An unexpected error occurred";
    setError(message);
    console.error("API Error:", error);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    error,
    handleError,
    clearError,
  };
}
