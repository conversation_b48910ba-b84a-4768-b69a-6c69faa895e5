@echo off
echo 🚀 Tarjama.ai Simple Start...

REM Stop and clean everything
echo [INFO] Cleaning up...
docker-compose down >nul 2>&1
docker volume rm tarjama_postgres_data >nul 2>&1

REM Start fresh PostgreSQL with default settings
echo [INFO] Starting PostgreSQL with default settings...
docker-compose up -d postgres redis minio

REM Wait for PostgreSQL
echo [INFO] Waiting for PostgreSQL to start...
timeout /t 15 /nobreak >nul

REM Test connection
echo [INFO] Testing database connection...
docker-compose exec postgres pg_isready -U postgres

REM Setup database schema
echo [INFO] Setting up database schema...
cd apps\api
call npx prisma generate
call npx prisma db push
cd ..\..

echo [SUCCESS] Database setup complete!
echo [INFO] Starting development servers...
echo.
echo [INFO] Services will be available at:
echo   - Web App: http://localhost:3000
echo   - API: http://localhost:3001
echo.

REM Start development servers
npm run dev
