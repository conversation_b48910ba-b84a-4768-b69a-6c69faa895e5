import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findById(id: string) {
    return this.prisma.findUserById(id);
  }

  async findByEmail(email: string) {
    return this.prisma.findUserByEmail(email);
  }

  async create(data: any) {
    return this.prisma.createUser(data);
  }

  async update(id: string, data: any) {
    return this.prisma.updateUser(id, data);
  }
}
