import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { User, UserRole, PlanType } from "@prisma/client";
import * as bcrypt from "bcryptjs";

export interface UpdateUserDto {
  name?: string;
  avatar?: string;
  preferences?: any;
}

export interface UpdatePasswordDto {
  currentPassword: string;
  newPassword: string;
}

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(private readonly prisma: PrismaService) {}

  async findById(id: string): Promise<User | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        include: {
          company: true,
          _count: {
            select: {
              documents: true,
              translations: true,
              glossaries: true,
              liveSessions: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException("User not found");
      }

      return user;
    } catch (error) {
      this.logger.error(`Error finding user by ID ${id}:`, error);
      throw error;
    }
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { email },
        include: {
          company: true,
        },
      });
    } catch (error) {
      this.logger.error(`Error finding user by email ${email}:`, error);
      throw error;
    }
  }

  async updateProfile(id: string, updateData: UpdateUserDto): Promise<User> {
    try {
      const user = await this.findById(id);

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
        include: {
          company: true,
        },
      });

      this.logger.log(`User profile updated: ${id}`);
      return updatedUser;
    } catch (error) {
      this.logger.error(`Error updating user profile ${id}:`, error);
      throw error;
    }
  }

  async updatePassword(
    id: string,
    passwordData: UpdatePasswordDto
  ): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: { id: true, password: true },
      });

      if (!user || !user.password) {
        throw new BadRequestException("User not found or password not set");
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(
        passwordData.currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        throw new BadRequestException("Current password is incorrect");
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(passwordData.newPassword, 12);

      await this.prisma.user.update({
        where: { id },
        data: {
          password: hashedNewPassword,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Password updated for user: ${id}`);
    } catch (error) {
      this.logger.error(`Error updating password for user ${id}:`, error);
      throw error;
    }
  }

  async getUserStats(id: string) {
    try {
      const user = await this.findById(id);

      const stats = await this.prisma.user.findUnique({
        where: { id },
        select: {
          usageQuota: true,
          _count: {
            select: {
              documents: true,
              translations: true,
              glossaries: true,
              liveSessions: true,
              learningCards: true,
            },
          },
        },
      });

      // Get recent analytics
      const recentAnalytics = await this.prisma.userAnalytics.findMany({
        where: { userId: id },
        orderBy: { date: "desc" },
        take: 30,
      });

      return {
        ...stats,
        recentAnalytics,
      };
    } catch (error) {
      this.logger.error(`Error getting user stats ${id}:`, error);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      const user = await this.findById(id);

      // Soft delete by updating the user record
      await this.prisma.user.update({
        where: { id },
        data: {
          email: `deleted_${Date.now()}_${user.email}`,
          name: "Deleted User",
          avatar: null,
          updatedAt: new Date(),
        },
      });

      this.logger.log(`User soft deleted: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting user ${id}:`, error);
      throw error;
    }
  }
}
