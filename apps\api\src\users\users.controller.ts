import {
  Controller,
  Get,
  Put,
  Delete,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from "@nestjs/common";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiBody,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import {
  UsersService,
  UpdateUserDto,
  UpdatePasswordDto,
} from "./users.service";

@ApiTags("users")
@Controller("users")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth("JWT-auth")
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get("profile")
  @ApiOperation({ summary: "Get current user profile" })
  @ApiResponse({
    status: 200,
    description: "User profile retrieved successfully",
  })
  async getProfile(@Request() req: any) {
    return await this.usersService.findById(req.user.id);
  }

  @Put("profile")
  @ApiOperation({ summary: "Update user profile" })
  @ApiResponse({ status: 200, description: "Profile updated successfully" })
  @ApiBody({ type: Object, description: "Profile update data" })
  async updateProfile(@Request() req: any, @Body() updateData: UpdateUserDto) {
    return await this.usersService.updateProfile(req.user.id, updateData);
  }

  @Put("password")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: "Update user password" })
  @ApiResponse({ status: 204, description: "Password updated successfully" })
  @ApiBody({ type: Object, description: "Password update data" })
  async updatePassword(
    @Request() req: any,
    @Body() passwordData: UpdatePasswordDto
  ) {
    await this.usersService.updatePassword(req.user.id, passwordData);
  }

  @Get("stats")
  @ApiOperation({ summary: "Get user statistics and usage data" })
  @ApiResponse({
    status: 200,
    description: "User statistics retrieved successfully",
  })
  async getUserStats(@Request() req: any) {
    return await this.usersService.getUserStats(req.user.id);
  }

  @Delete("account")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: "Delete user account" })
  @ApiResponse({ status: 204, description: "Account deleted successfully" })
  async deleteAccount(@Request() req: any) {
    await this.usersService.deleteUser(req.user.id);
  }
}
