import { CheckIcon } from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Real-time Translation',
    description: 'Instant Arabic-English translation during live meetings and conversations.',
    icon: CheckIcon,
  },
  {
    name: 'Document Processing',
    description: 'Upload PDFs, images, and documents for accurate translation with context.',
    icon: CheckIcon,
  },
  {
    name: 'Construction Terminology',
    description: 'Specialized AI trained on construction industry terminology and context.',
    icon: CheckIcon,
  },
  {
    name: 'Team Collaboration',
    description: 'Share glossaries, translations, and collaborate with your team.',
    icon: CheckIcon,
  },
  {
    name: 'Learning System',
    description: 'Personalized vocabulary building from your translated documents.',
    icon: CheckIcon,
  },
  {
    name: 'Secure & Compliant',
    description: 'Enterprise-grade security with GDPR and regional compliance.',
    icon: CheckIcon,
  },
];

export function FeaturesSection() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Everything you need for construction translation
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Purpose-built features for construction professionals working across Arabic and English languages.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {features.map((feature) => (
              <div key={feature.name} className="flex flex-col">
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7">
                  <feature.icon className="h-5 w-5 flex-none text-primary" aria-hidden="true" />
                  {feature.name}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p className="flex-auto">{feature.description}</p>
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  );
}
