"use client";

import { motion } from "framer-motion";

const features = [
  {
    name: "Real-time Translation",
    description:
      "Instant Arabic-English translation during live meetings and conversations with 99.5% accuracy.",
    icon: "🌐",
  },
  {
    name: "Document Processing",
    description:
      "Upload PDFs, images, and documents for accurate translation while preserving formatting.",
    icon: "📄",
  },
  {
    name: "Construction Terminology",
    description:
      "Specialized AI trained on 10,000+ construction industry terms and technical context.",
    icon: "🏗️",
  },
  {
    name: "Team Collaboration",
    description:
      "Share glossaries, translations, and collaborate with your team in real-time.",
    icon: "👥",
  },
  {
    name: "Learning System",
    description:
      "Personalized vocabulary building from your translated documents and usage patterns.",
    icon: "📚",
  },
  {
    name: "Secure & Compliant",
    description:
      "Enterprise-grade security with bank-level encryption and international compliance.",
    icon: "🔒",
  },
];

export function FeaturesSection() {
  return (
    <section className="py-20 bg-gray-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Powerful Features for{" "}
            <span className="bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">
              Construction Professionals
            </span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Everything you need to break down language barriers and accelerate
            your construction projects across the Middle East.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-8 hover:bg-gray-900/70 transition-all duration-300 group"
            >
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                {feature.name}
              </h3>
              <p className="text-gray-400 leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
