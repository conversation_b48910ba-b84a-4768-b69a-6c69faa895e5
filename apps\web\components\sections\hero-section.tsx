import Link from 'next/link';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { But<PERSON> } from '@/components/ui/button';

export function HeroSection() {
  return (
    <section className="relative py-20 md:py-32">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
            AI-Powered{' '}
            <span className="text-gradient">Arabic-English Translation</span>{' '}
            for Construction
          </h1>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Transform your construction projects with specialized translation technology. 
            Real-time translation, document processing, and team collaboration designed 
            specifically for construction professionals.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Button size="lg" asChild>
              <Link href="/register">
                Get Started Free
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/demo">
                Watch Demo
              </Link>
            </Button>
          </div>
          <div className="mt-16 flow-root sm:mt-24">
            <div className="relative rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10 lg:rounded-2xl lg:p-4">
              <div className="aspect-video rounded-lg bg-white shadow-2xl ring-1 ring-gray-900/10">
                <div className="flex h-full items-center justify-center text-muted-foreground">
                  Product Demo Placeholder
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
